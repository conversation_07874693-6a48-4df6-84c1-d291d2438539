% !TeX root = ../main.tex

\begin{translation}
\label{cha:translation}

\title{无人机远程识别：教程与综述}
\maketitle

\tableofcontents

无人机远程识别是一种新兴技术，它允许地面观察员识别空域中的无人机，并获取有关无人
机及其操作员的信息。这种技术的目标是加强人员和夜间的安全操作，保护公共隐私。远程识别
的模式有两种：基于广播和基于网络。尽管这两种模式的技术实现似乎都很简单，但远程识别的
挑战在于，它需要考虑多种需求，包括人员安全、飞行器安全、信息隐私和业务需求本身。目前，
为了完善这项技术，人们正在监管、标准化、设计、实施和测试方面做出巨大努力。本文旨在概
述这些努力的概况，同时作为一篇教程和综述，向监管机构、标准化组织、行业和研究人员介绍
这项技术的最新进展，并强调其机遇和挑战。

关键词：远程识别、UAV、UTM、RID法规、RID标准

\section{引言}

无人机的数量正在增长，今年将超过500万架，无人
机的销售额将超过120亿美元，到2025年，集成无人机
系统的潜在经济效益将产生约820亿美元[1]。无人机，
即无人机，由于使用灵活性和相对较低的运营成本等多
种功能而越来越受欢迎。物流服务[2]、交通监控[3]、
农业[4]、军事侦察[5]和执法监视[6]是推动该领域研究
的应用示例。管理预期的大量空中交通是广泛采用这项
技术的最大挑战。不同的威胁限制了公众对无人机技术
的接受，如间谍活动、物理碰撞和携带爆炸物[7]。此
外，传统的空中交通管理（ATM）系统不适合满足无
人空中交通所需的自主性和机动性[8]。因此，世界各
国政府都在努力维护一个安全的ATM系统。该系统旨
在确保无人机具有足够的自主性和机动性，同时提高公
众对低空城市空中交通的接受度[9]。

文献中对低空空域法规的执行略有提及。然而，拟议
的解决方案存在严重缺陷。例如，Rahman等人[10]利用
无线网络提出了一种低空空域无人机的政策执行系统。
所提出的方法依赖于将无人机的坐标记录到云服务器上，
在那里将记录的路径与批准的任务路线进行比较。通过
这种方式，系统可以确定无人机是否在其分配的走廊上
飞行。这种解决方案不仅需要电力，而且恶意无人机操
作员可以轻松操纵坐标以避免跟踪和处罚。Yazdinejad
等人[11]采用了一组在地理区域内分配的服务器来解决
策略执行问题。服务器用于在无人机进入特定走廊时进
行身份验证。该解决方案的实施成本高昂，缺乏可扩展
性，因为它需要在每个区域安装大量服务器来执行身份
验证和授权规则。[12] 强调了与执法和国家安全机构在
探测、定位和识别非法无人机方面的能力有关的多个问
题。因此，目前采用无人机应用的主要障碍是缺乏一个
有效的监控系统来执行城市附近引入的规章制度。

为了克服其中一些挑战，许多国家的民航机构正在强
制部署远程身份识别。简而言之，远程识别
可以被描述为无人机的数字牌照。RID的最终目标是提
供实时识别和位置信息，供公众和当局用于监控空域和
惩罚非法行为。欧盟航空安全局（EASA）于2020年4月
发布了修订后的远程识别法规（（EU）2020/1058）。
这些规定要求所有无人驾驶飞机都应配备远程识别系统
[13]。美国联邦航空管理局于2021年1月发布了远程识
别的最终规则[14]。航空当局定义的远程ID法规通常是
基于性能的，没有确切的支持技术规范。相反，这些法
规经常将技术标准作为可能的合规方式。其中一项标准
是美国材料与试验协会（ASTM）远程ID和跟踪标准规
范[15]。ASTM定义了两种无人机远程识别技术：基于
网络和基于广播。基于网络的方法有助于通过互联网在
全球专用服务器上提供远程身份信息。在广播模式下，
无人机通过蓝牙或Wi-Fi单向通信在本地传输其身份信
息，而不使用互联网协议。本地观察者可以使用支持所
提出的通信链路的任何手持设备实时接收远程ID。这种
远程识别机制被认为是检测、识别、跟踪和管理城市空
域内无人机的可靠方法。

子鹤和田介绍了广播和网络远程识别的体系结构，并
回顾了美国、欧洲、瑞士、日本和中国的注册情况[16]。
我们也不知道有任何关于这项新技术的调查工作。文献
缺乏对远程识别的范围、机遇和挑战的全面综述。本文
通过对新兴的远程识别技术进行深入调查，缩小了这一
差距。尽管其技术实现看起来很直接，但RID技术非常
复杂，涉及多个利益相关者。如图1所示，该领域的相
关活动可分为四大类。作为公共资产，空域安全首先是
政府的责任。因此，世界各地的民航当局正在通过发布
相关法规来领导远程识别活动。这些规定与多个组织的
标准化工作齐头并进。法规和标准似乎在很大程度上相
互影响。另一方面，技术能力影响了法规和标准化活动。
例如，移动设备的能力和互联网连接的缺乏影响了民航
当局规定的远程识别模式。远程识别的研究正在兴起，
我们将回顾和讨论的文献中只有少数贡献。

本文根据这些活动类别进行组织。以下四个部分将进
行回顾
相关法规、标准、技术和研究。第七节讨论了RID技术，
概述了其机遇和挑战，并提供了一些未来的方向。

\section{相关法规}

无人机行业是一个多元化和创新的领域，具有丰富就
业市场的巨大潜力。因此，对于工业化国家来说，至关
重要的是引入一套政策和法规，为无人机融入其国家空
域铺平道路，同时为其他空域用户和公众保持高度的安
全、隐私和安保。在大多数情况下，这些国家的政府已
经为商业和娱乐无人机运营商制定了一般规则，例如强
制性无人机登记、避免在超视距（BVLOS）模式下运
行、飞越禁飞区或夜间飞行。一些国家进一步制定了管
理无人机系统在空域运行的框架，如美国[17]、欧盟
[18]、俄罗斯[19]和中国[20]。[21]中提供了对这些框架
和政策的详细审查。

尽管做出了这些努力，但最近只有少数政府意识到实
时无人机系统识别的重要性，使安全机构和执法部门能
够：i）识别潜在威胁，ii）实时响应，iii）收集足够的
信息进行调查和取证。因此，本节调查了与RID技术相
关的现行法规，重点介绍了技术和法律方面。表1简要
概述了我们的调查。

\subsection{美国}

为了跟上娱乐和商用无人机的激增，美国联邦航空局
通过引入标准和法规，努力确保空域生态系统的安全运
行。自2016年UTM项目启动以来，已经发布了几项规
则制定，该项目是美国国家航空航天局（NASA）和美
国联邦航空局之间的合作[29]。这个为期四年的项目包
括在多个州进行的几次测试，以展示实现安全可靠的
UTM所需的技术。

2016年6月，美国联邦航空管理局发布了小型无人驾
驶飞机系统运行和认证的最终规则[30]。从那时起，美
国联邦航空管理局预计需要制定另一项规则，以促进安
全可靠的低空作战。根据美国联邦航空管理局的说法，
远程识别是将无人机系统完全整合到国家空域的关键先
决条件[22]。同年，美国联邦航空管理局成立了航空规
则制定委员会（ARC），为无人机对人的操作制定联邦
监管框架，该框架还就可用的RID技术提出了建议[31]。

2019年12月，美国联邦航空管理局发布了远程识别拟
议规则制定通知[32]，寻求
公众意见，以最终确定RID规则。在2021年1月宣布最
终规则之前，美国联邦航空管理局收到并审查了超过
53000条评论[22]。该规则将RID定义为一种数字车牌，
重量超过0.55磅的无人机在飞行时应该播放。尽管如此，
重量小于0.55磅的无人机在飞越人员或夜间时也需要广
播RID[33]。此外，最终规则确定了构成RID的基本信息：
无人机的唯一标识符、位置、操作员的位置、无人机的
速度、时间戳和紧急标志。然而，该信息可以由广播范
围内的任何无线设备接收。最终规则规定，只有美国联
邦航空管理局才能将无人机的ID与操作员的个人信息相
关联[34]。它确保了空域用户的隐私得到保护。

最初在建议制定规则的通知中，美国联邦航空局为现
有（传统）无人机提出了一种基于网络的RID解决方案
（该解决方案将在第六节中详细介绍）。然而，为了回
应公众的意见，该解决方案被取消，取而代之的是可以
嵌入或连接到现有无人机的RID广播模块[22]。因此，
根据第107部分[33]飞行的所有符合条件的无人机都必
须通过现有个人无线设备可接收的射频广播其RID。广
播模块的工作频率仅限于未经许可的频谱，即900MHz、
2.4GHz或5.8GHz。广播模块应内置于新制造的无人机
中，而传统无人机则允许使用附加模块。此外，最终规
则强调了最大化RID广播范围的重要性。它进一步指出，
应制造无人机以防止操作员禁用RID功能。

总之，美国联邦航空局关于RID的最终规定要求运营
商注册他们的无人机，并通过内置RID模块或可以连接
到无人机的独立外部模块广播定义的RID。后一类不允
许在视线之外进行操作。第三类允许运营商在社区组织
和教育机构可用的FAA认可识别区（FRIA）内无需广
播RID即可飞行[34]。FRIA内的操作仅限于视线范围。
最后，美国联邦航空局禁止无人机使用ADS-B输出和
ATC应答器，以限制对载人航空通信的可能干扰。这些
RID最终规则将于2023年9月起生效[22]。

\subsection{欧洲}

在欧洲，多个机构和组织呼吁建立一个共同框架，为
无人机安全可靠地融入空域铺平道路。因此，已经采取
了几项举措。也许，欧盟委员会在2015年3月的《里加
宣言》中率先强调了远程识别无人机飞行员的必要性。
他们将这种远程识别框架设想为“电子身份芯片”
称之为IDrones。欧洲单一天空ATM研究（SESAR）联
合承诺（SJU）也正式将无人机系统集成到欧洲空域。
他们发布了多个版本的美国空间作战概念（ConOps）
[18]。

2015年12月，欧洲航空安全局发布了一份关于“引入
无人驾驶飞机运营监管框架”的技术意见[35]。该文件强
调了登记和身份识别在改善执法方面的作用
空域规则和条例。技术意见进一步设想了使用蜂窝网络
或射频识别（RFID）技术等现有技术实现无人机识别
系统的技术。

其他组织和委员会也意识到，登记和身份识别是将无
人机系统安全可靠地融入城市空域的一项核心要求。这
些组织的例子包括欧洲无人机联盟（DAE）、国际机场
协会
（ACI）和全球UTM协会（GUTMA）。DAE主张UTM
系统应配有可靠的登记和身份识别系统[37]。除了无人
机识别外，ACI还建议使用无人机任务识别号来促进风
险评估程序[38]。另一方面，GUTMA的几篇出版物[39]、
[40]强调了远程识别在实现安全可靠的UTM生态系统中
的作用。

2016年8月，欧洲航空安全局发布了一份原型“无人驾
驶飞机运行委员会条例”[41]，该条例将电子识别定义为
“在没有直接物理访问的情况下识别飞行中的无人驾驶飞
机的能力”它进一步详细说明，“系统应根据欧洲航空安
全局可接受的标准传输以下数据（如适用）：运营商的
注册；无人机系统的类别；无人机系统的操作类型；其
地理围栏的状态；它的位置和高度。”后来，欧洲航空安
全局发布了一系列规则[23]、[42]，详细说明了无人机电
子识别的要求和规范。这些eRules定义了UAS的两个主
要类别，即：开放类别和特定类别。后一类的特点是无
人机系统具有运送人员和危险品以及在人群中运行的能
力等特殊功能。在这一类别下运行的无人机需要国家航
空航天局的特别授权。另一方面，根据无人机系统的重
量和规格，开放式类别进一步分为五类。2021年9月发
布的最新eRule[23]中增加了另外两个类。在开放式类别
中操作涉及避免在人身上直接操作，保持VLOS操作或
使用无人机观察员，在低于120米的高度飞行，并且只携
带非危险有效载荷。根据这一规则，远程飞行员必须确
保远程识别系统处于活动状态并保持最新。

该规则还规定了设计和制造远程识别附加组件的要求。
主要是，它要求远程识别附加模块的制造商为远程识别
模块分配类型和序列号，使其符合RID的要求。在120
米以下高度运行的特定类别的无人机系统需要在飞行过
程中使用开放和有记录的传输协议定期传输直接远程识
别（DRID）。SESAR联合承诺将DRID[18]定义为一种
发射的“信号，手持设备可以直接接收该信号进行识别，
或使用该信号所包含的数据向U空间电子识别服务请求
更多信息”传输的RID应至少包含无人机操作员注册号
和注册时提供的3位验证码、符合标准ANSI/CTA-2063
a-2019的无人机唯一物理序列号、时间戳、无人机的位
置和高度、航线、地面速度、远程飞行员位置和紧急标
志。

此外，在开放类别中运行且重量小于250克（C0类）
的无人机可免于传输RID。另一方面，在C1-C3类开放
式类别下运行的无人机须遵守特定类别的相同DRID要
求。然而，在开放类别中操作允许另一种形式的远程识
别：网络远程识别系统（NRID）。NRID和DRID这两
个方案都需要共享与上述相同的信息。NRID涉及通过
网络共享这些信息，而不是通过直接传输或广播。[23]
中没有提供有关C5和C6类远程识别适用性的信息。

与美国联邦航空管理局的规定一样，欧洲航空安全局
也建议提供防篡改的远程识别系统。欧洲航空安全局还
要求制造商在说明手册中提供有关DRID排放所用传输
协议的信息。欧洲的无人机运营商必须在2023年1月前
遵守这些规则。

\subsection{俄罗斯}

俄罗斯联邦交通部已经意识到将无人机系统融入城市
空域的必然收益。俄罗斯UTM（RUTM）运营概念是由
Aeronet国家技术倡议公司开发的[19]。然而，无人机远
程识别的标准和技术尚未最终确定[43]。在初始阶段，
远程识别预计将基于ADS-B 1090 ES等现有技术解决方
案。其他识别方式，如移动数据传输网络，将在未来阶
段予以考虑。

\subsection{中国}

作为一个工业大国，中国对商用无人机的应用表现出
了无限的兴趣[16]。因此，中国民用航空局（CAAC）
发布了四项规则，以规范城市环境中的无人机。第一个
规则制定（AC-91-FS-2015-31）于2015年发布，尽管发
布了关于同一主题的其他三个规则制定，但仍然有效。
根据该文件，除微型（<1.5公斤）无人机外，所有无人
机都需要保持与无人机云交换系统（UACES）的连接，
以便在飞行过程中报告其当前位置和状态。它本质上类
似于NRID方案。截至2020年，[45]中列出了11个经过认
证的无人机云系统（UACS）。另一方面，行业合作伙
伴发布的两项标准[24]、[46]规定了UACS和无人机之间
的信息交换要求。此外，还指定了各种网络安全技术，
包括数字签名和区块链。

2021年3月8日发布的最新规则制定[24]侧重于以更符
合FAA框架的方式实施远程ID，包括对RID消息元素和
传输技术的要求，即：NRID和BRID。

最后，根据[47]，中国民用航空局可能要求“ADS-B雷
达定期被动报告作战细节，无需飞行员参与无人机云系
统”

\subsection{日本}

2020年6月，日本对《民用航空法》进行了修订，规
定无人机系统必须在2022年6月注册。根据[25]，政府
还计划引入DRID作为强制性要求，以允许实时识别无
人机。原则上，提出了四种DRID传输技术：蓝牙5.x、
蓝牙LE远程、Wi-Fi邻居感知网络和Wi-Fi信标。无论采
用何种传输技术，RID广播的拟议频率至少为每秒一次。
此外，土地、基础设施、交通和旅游部长规定，RID信
号必须符合ASTM国际F3411-19标准，并包括有关注册
ID、UAS序列号、位置、UAS矢量信息和身份验证信息
的信息。

\subsection{印度}

2021年8月，印度民航局宣布了一套规则，以规范无
人机在城市空域的操作[26]。根据无人机系统认证所需
的强制性安全功能，该文件指出，中央政府将来可能会
要求运营商安装一些安全功能，如“无许可-无起飞硬件
和固件”和“传达无人机系统位置、高度、速度和唯一识
别号的实时跟踪信标”

\subsection{新加坡}

根据民航总局局长（DGCA）最近发布的一份文件
[27]，在超视距内运行的无人机系统必须配备视觉或听
觉警告指示器，以便在接近着陆区时提醒附近人员。据
我们所知，该国的官方监管文件中没有关于无人机识别
的进一步要求。

\subsection{阿拉伯联合酋长国}

阿联酋正在采取一项国家战略，促进该国成为尖端技
术的中心。在本文中，通用民用航空管理局（GCAA）
制定了一个框架来规范无人机在受控空域的使用[48]。
一般来说，这些规则要求无人机操作员在飞行前注册他
们的无人机，获得飞行员证书，避免超视距超视距操作，
并避免飞越禁飞区。然而，没有明确规定在飞行时识别
无人机的要求。相反，同一文件第70条规定，驾驶无人
机“不带国籍和登记标志或显示不正确或不合格的标志”
的最高罚款为100000。迪拉姆民航管理局DCAA于2020
年6月发布了另一项关于同一主题的最新规则[28]。

它为无人机在受控空域的操作提供了更详细的框架。尽
管如此，它缺乏对无人机在飞行期间所需识别的清晰描
述。特别是，规则制定几乎没有触及第15条中的远程识
别问题，如下所示：“任何人不得使用无人驾驶飞机或
进行无人驾驶飞机的操作测试，除非其注册号或代码，
或DCAA规定的任何其他识别信息显示在其上。”此外，
Raj等人[49]报告称，无人机未安装警示灯的罚款约为
5000迪拉姆。另一方面，民用航空条例（CAR）文件
[50]没有强调无人机的任何飞行中识别要求。尽管有这
些有限的识别指南，但阿联酋目前的无人机法规缺乏一
个符合最佳国际实践的完全监管的远程识别框架。

\section{标准}

与任何其他领域一样，在开发和实施新技术时制定一
套需要遵守的规则至关重要。从意识形态上讲，标准化
范式涉及多个方面，如产品制造和维护、运营和程序、
交通管理和子系统。本节重点介绍解决远程识别问题的
最新标准，如ASTM、3GPP DRIP和CTA。值得注意的
是，在撰写本文之前，许多专门针对远程识别的标准仍
在开发中（例如ISO/DIS 23629-8）。

\subsection{消费者技术协会}

消费者技术协会（CTA）引入了与远程ID相关的标准
ANSI/CTA-2063，小型无人机系统序列号[51]。车载电
子委员会WG 23无人机系统（UAS）和CTA R6便携式
手持设备制定了该标准。根据CTA-2063标准，两种类
型的序列号可用于识别无人机：物理和电子。物理序列
号（PSN）由持有制造商和无人机身份的利益相关者分
配给所有无人机。另一方面，电子序列号（ESN）表示
配备有软件版本和性能特征的国际移动台的身份[52]。
在以下小节中，我们将根据CTA-2063标准解释PSN和
ESN成分。

\subsubsection{物理序列号}

PSN由三个基本组件组成，如图2所示：制造商代码
（MFC）、长度代码（LC）和制造商分配的唯一序列
号（USN）。CTA及其利益相关者负责制定和定义MFC
的法规。MFC的长度为四个字符，包括数字和大写字母
的任意组合，字母“O”和“I”除外。LC
是反映PSN序列号中字符数的单个字母字符。最后，制
造商负责生成USN并将其分配给每个UAS产品。USN长
度范围为1到20个字符，可以通过单个字符传达。
使用字母A到T表示LC，其中A=1和T=20。USN是一个
字母数字代码，应包括任何除字母“O”和“I”外的数字和
大写字母的组合。图2中的底部矩形表示
PSN=“DRN1M9876543210CBA”的示例，其中
MFC=“DRN1”，LC=“M”表示13USN字符，
USN='9876543210CBA'。

\subsubsection{电子序列号}

图3展示了电子序列号（ESN）格式，它由四个字段
组成。从PSN继承的前两个字段是MFC和USN。另外两
个是国际移动台标识（IMSI），紧随其后的是软件版本
和性能特征（PC）。与PSN不同，ESN具有47个ASCII
码字符的预定义长度。根据标准，如果USN的长度小于
20个字符，制造商必须在USN的开头附加零。

在图3的底部，我们展示了第IV-A1小节中提到的相同
示例。信用证不包含在ESN格式中。软件版本的IMSI以
16个字符的十进制数字代码表示，由3GPP TS 23.003[53]
定义。如果不使用移动台设备和软件版本的信息，则该
字段应用16个字节的0×00值填充。最后，PC字段由为
无人机系统的性能特点保留的七个字符组成。第一个字
符表示CTA管理其分配以定义性能特征的标准机构。如
果未使用七个性能特征字符中的任何一个，则应将其填
充为零。

\subsection{美国材料与试验协会}

美国材料与试验协会（ASTM）标准规范为在各种环
境下在极低空空域运行的无人机的RID制定了性能要求。
它通过消除匿名因素来确保无人机运营商的问责制，
同时保护其企业和客户的运营隐私。此外，它还定义了
两种广播模型的消息格式、传输方法、最低性能标准和
测试要求[15]。

\subsubsection{概念概述}

图4显示了根据ASTM标准的RID的一般概念。ASTM
标准的范围仅限于无人机和用户应用程序之间的接口，
不考虑广播接收器硬件。特别是，该规范侧重于使用蓝
牙和Wi-Fi传输协议，并涵盖了现有技术提供的传输协
议。网络RID没有使用特定的技术，因为它需要无人机
和最终用户的蜂窝网络覆盖。

\subsubsection{性能要求}

本小节强调了传输RID消息的最低性能要求和传输机
制。我们总结了在实施ASTM标准期间应考虑的关键信
息，我们强烈建议参考ASTM标准了解任何细节。

根据ASTM标准，存在两种类型的广播消息，即静态
和动态。静态数据，如无人机的识别号，在飞行过程中
是不可更改的。相比之下，动态数据会发生变化，例如
经度和纬度。动态消息应至少每秒发送一次，而静态消
息应每三秒发送一次。自位置/矢量消息中动态字段的
适用时间起经过的最大潜在时间不得超过1秒。RID收
发器应提供以全向模式发射的足够功率。最小传输有效
各向同性辐射方向图（EIRP）定义为传输方向图水平
面内所有360度远场周围的最小EIRP。整个平面上的最
小EIRP不得小于各国国家波浪法确定的预定义阈值。例
如，Wi-Fi收发器的EIRP美国应至少为15dBm。

ASTM标准主要关注Wi-Fi和蓝牙介质来描述传输机
制，因为它们广泛部署在常用的设备中。主要地，该实
现方法利用用于声明设备可用于配对的广告信标消息。
蓝牙使用信道（37、38和39）将消息广播到非特定端点
（无连接），而Wi-Fi为此保留信道6。ASTM标准描述
了Wi-Fi技术作为一种无连接广播机制的可用性，该机
制使用Wi-Fi管理帧封装Open Drone消息。消息应在服
务发现帧内编码，
以使数据可用于显示而无需任何无线连接。当分别使用
2.4GHz和5.745GHz频带时，NAN Discovery仅在信道6
和149中运行。此外，NAN是Wi-Fi Aware使用的底层规
范，它包含了通过使设备能够在没有网络基础设施或设
置过程的情况下交换服务和信息来增强对等通信的能力。
消息可以在单个包中一起发送，也可以通过将消息划分
为不同的包来动态发送。

\subsubsection{消息格式}

图5显示了BLE 4.x和BEL 5.x在广播期间使用的数据
包的格式和值。NAN服务发现帧在低级别是相同的，
BLE数据包中每个报告的主要区别在于高报头级别。每
个广播消息都有一个以一个字节编码的报头，其中四个
MSB位用于定义消息类型，四个LSB位用于确定协议版
本。消息头后面是24个字节的数据，这些数据应该根据
预定义的格式并使用标准数据字典进行编码。因此，当
广播数据小于24个字节时，每个广播消息的大小为25个
字节，并用空值填充。

蓝牙技术支持通过信标信道传输广播帧，自定义消息
大小为31字节，为广播消息协议（Open Drone ID）提
供25个字节，为额外的报头数据提供其他字节。
Bluetooth 5.x是
BLE 4.x集成了新功能，允许远程通信和广告扩展。应
调整数据包的“可梦字节”，以将广告范围增加四倍。通
过在主信标中实现指针，引导接收器从辅助信道读取，
扩展的广告功能允许在非信标信道上最多255个字节。

\subsection{互联网工程工作组}

互联网工程任务组（IETF）发现了当前远程识别法规
和技术标准中的一个根本差距。特别是，这些法规和标
准大多将无人机识别视为目的，而不是支持空中交通管
制或无人机通信等其他应用的手段。例如，目前的标准
和法规几乎没有提供任何信息，使观察员能够与飞行员
沟通，以获取有关无人机系统运行的更多信息，或在紧
急情况下请求离开空域。IETF主张，远程识别不应仅用
于识别，其功能应扩展以支持相关应用。为了实现这一
目标，IETF旨在利用现有的互联网标准、基础设施和商
业模式进行域名注册，以开发必要的协议来保护运营商
的隐私，实现强身份验证，并使授权方能够立即使用信
息。

为了实现这一目标，IETF于2020年2月成立了一个工
作组，为无人机远程识别协议（DRIP）制定一个开放
标准。
IETF工作小组旨在使DRIP规范与国家和国际监管要求
保持一致，例如国际民用航空组织、欧盟航空安全局和
美国联邦航空管理局发布的要求。此外，DRIP基于
ASTM F3411-19中规定的链路层，增强了其对应用的支
持。

\subsubsection{DRIP要求规范}

IETF最近发布了一份征求意见文件（RFC 9135），其
中定义了DRIP工作组制定的解决方案的术语和要求[54]。
本文档强调，当无人机标识符选择得当时，各种互联网
协议和服务可用于支持RID基本安全功能之外的不同应
用。大多数互联网协议都需要一些标识符，如网络访问
标识符（NAI）、数字对象标识符（DOI）、统一资源
标识符（URI）、域名或公钥。为此，DRIP侧重于通过
实现以下目标，使通过UASRID获得的信息立即可用：

\begin{itemize}
  \item 确保RID的可信度。
  \item 启用验证UAS是否已注册RID和注册表，以便根
据已知的注册表审查对受信任的操作员进行分类。
  \item 促进无人机飞行数据（如位置和速度）的独立报
告，以确认或反驳用于UTM跟踪的操作员自我报
告。
  \item 允许授权方与远程飞行员建立安全通信。
\end{itemize}

RFC文档将DRIP要求分为四组：一般要求、标识符
要求、注册表要求和隐私要求。为简洁起见，表2显示
了这些要求的标签。为了描述这些要求及其理由，读者
可以参考[54]。

\subsubsection{DRIP架构}

从[54]中规定的要求开始，DRIP工作组正在开发一种
远程识别架构[55]。使用图6所示的参考场景。此场景
显示了多个观察者。他们中的一些人是公众，另一些人
是负责公共安全和安保的政府官员。多架无人机在观察
范围内飞行，每架无人机由其操作员通过指挥和控制链
路（C2）控制。无人机使用其ID通过车对车链路（V2）
进行通信，并通过车对基础设施连接（V2I）与地面服
务进行通信。该场景假设至少使用一个注册表来查找公
共信息，一个注册表用于查找与无人机及其操作员相关
的私人详细信息。最后，域名服务（DNS）解析所涉及
实体的各种标识符和定位器。

DRIP的核心架构方面是使用层次结构主机标识标签
（HHIT）作为自断言的IPv6地址，作为可信任的远程
标识符
无人机[56]。自我断言意味着，给定主机标识（HI）、
HHIT覆盖可路由加密哈希标识符（ORCHID）结构和
HHIT上的注册表签名，接收器可以验证HHIT。DRIP从
非对称密钥对构建远程ID。通过使用关联的私钥对此ID
进行签名，可以保证ID所有权的证明。特别是，无人机
ID是通过哈希HI公钥加密生成的。其哈希值，即无人机
ID，称为主机身份标签（HIT）。HIT通过密码散列函
数的第二个预映像电阻特性是唯一的[55]。

无人机应配备HHIT、HHIT的公钥和相应的私钥，以
实现消息签名。观察器设备应包含DRIP标识符根注册
表的公钥或从属注册表的证书。当使用爱德华兹曲线数
字签名算法（EdDSA）时，用作无人机ID的HHIT的自
我证明可以在短短84个字节内完成[57]。该证明由HHIT、
时间戳和EdDSA签名组成[55]。

DRIP标识符可以由制造商作为静态HHIT分配给无人
机，例如根据CTA2063编码为硬件序列号的单个HI和衍
生HHIT[51]，[55]。这种静态HHIT只应用于将一次性
DRIP标识符绑定到唯一的无人机。根据实施情况，这
可能会让制造商拥有HI私钥。一般来说，观察员可能需
要互联网接入来验证证明或证书。通过在具有注册表公
钥和一系列证明或证书的观察者设备上保留小缓存，可
以避免对连接的需求，假设信任路径上的所有各方都使
用HHIT作为其身份[55]。

\subsection{第三代合作伙伴计划}

3GPP已经开始了几项活动，通过移动网络解决无人
机系统的连接需求，包括5G系统。图7显示了3GPP工作
组开发的无人机参考模型[58]。因此，无人机通过蜂窝
网络连接。无人机操作员可以控制一架或多架无人机，
无人机系统与UTM系统交换应用数据流量。不使用
3GPP网络的命令和控制链路（C2）在3GPP范围内。

\subsubsection{3GPP对UAS远程识别的一般要求}

技术规范TS 22.125列出了无人机远程识别的17项一
般要求。因此，3GPP系统应提供以下内容[58]：

\begin{itemize}
  \item 启用UTM以关联无人机及其控制器
并将其识别为无人机系统。
  \item 向UTM提供无人机及其控制器的身份。
  \item 使无人机系统能够发送有关无人机的UTM数据，
如唯一身份、型号、供应商、起飞重量、位置、
所有者身份、任务类型、路线数据和操作状态。
  \item 使无人机系统能够发送有关无人机控制器的UTM
数据，如唯一身份、位置、所有者联系方式、操
作员许可证和飞行计划。
  \item 支持UAS和UTM之间数据交换的不同级别的身份
验证和认证。
  \item 提供可扩展的数据交换，以满足UTM的未来应用。
  \item 支持不同的标识符，包括国际移动设备识别码
（IMEI）、移动站国际用户目录号（MSISDN）、国际移动用户
标识（IMSI）或IP地址。
  \item 使UAS中的用户设备能够将这些标识符中的任何
一个发送到UTM。
  \item 使移动网络运营商（MNO）能够使用无人机及其
控制器的基于网络的定位信息来增强发送给UTM
的数据。
  \item 使UTM能够将授权操作的结果通知移动网络运营
商。
  \item 仅当存在适当的订阅信息时，才允许移动网络运
营商允许无人机系统授权请求。
  \item 使使无人机系统能够用无人机及其无人机控制器的
实时位置信息更新UTM。
  \item 向UTM提供无人机及其控制器的补充位置信息。
  \item 支持同时将无人机及其控制器连接到不同的公共
陆地移动网络（PLMN）。
  \item 使移动网络运营商能够获取有关无人机支持3GPP
通信能力的信息。
  \item 支持区分具有无人机系统功能的用户设备的无人
机和具有非无人机系统用户设备的无人驾驶飞机。
  \item 支持UTM检测未经授权的无人机。
\end{itemize}

\subsubsection{无人机远程识别3GPP参考体系结构}

3GPP发布了一份技术报告，其中包含用于无人机识
别和跟踪的新3GPP UAS网络功能标准，以支持远程识
别[59]。这项研究强调了从无人机识别开始的七个关键
问题，提出了以下问题：

\begin{itemize}
  \item 在3GPP系统中，为无人机和/或无人机控制器分配
了哪些身份？
  \item 3GPP系统中的无人机或控制器如何使用身份？
  \item 与3GPP系统之外的各方交换哪些身份？
  \item 3GPP系统如何与UTM交互以实现无人机识别？
\end{itemize}

该研究探讨了解决这一问题的不同解决方案
报告了关键问题。最佳解决方案之一包括将无人机参考
架构中的网络实体和接口映射到3GPP参考架构。图8显
示了用于无人机远程识别的3GGP参考架构的概述，有
关内部架构的更多详细信息，请参阅[59]。总之，无人
机应该有两个身份：

\begin{itemize}
  \item 民航局一级识别码，它由无人服务供应商（USS）在注册期间分配给无人
机，其中包括UUID的序列号等信息。CAA-ID用于UTM
参与者识别无人机，并在网络和广播模式下进行远程识
别。该架构支持3GPP系统之外的实体（例如执法部门）
可用的机制，以解析CAA标识并发现相应无人机的USS。
3GPP系统由无人机提供CAA-ID，并且在向UTM/USS
提供移动网络运营商（MNO）服务时，它可以选择性
地将此身份提供给UTM/USS。
  \item 3GPP ID，它由移动网络运营商（MNO）提供给无人机，或由
接入和移动性管理功能（AMF）或会话管理功能
（SMF）提供给UAS。3GPP系统使用它来识别无人机。
3GPP ID包括有关使用的订阅标识的信息，例如通用公
共订阅标识符（GPSI）和移动台综合业务数字网
（MSISDN）、分配给协议数据单元（PDU）会话的IP
地址，或3GPP设备标识符，如永久设备标识符（PEI）
和国际移动台设备识别码（IMEI）。USS使用3GPP ID来调用MNO服
务（例如，曝光功能或位置服务）或在授权期间。
3GPP ID采用GPSI的格式，并且至少支持外部标识符。
3GPP网络在不与USS/UTM交互的情况下分配外部标识
符，并且必须在3GPP网络的地理区域内（例如，至少
是国家）是唯一的。
\end{itemize}

\section{技术}

\subsection{RID技术概述}

本节回顾了先前的RID技术，以帮助社区选择无人机
RID的最佳技术，并模拟对执行无人机远程识别的设备
的要求。根据ASTM的定义，RID技术可分为两大类，
如图9所示。首先，直接广播技术将无线电信号从无人
机传输到最近的地面接收器，如图9a所示。另一个如图
9b所示，基于无人机通过互联网与空中交通服务（ATS）
系统的连接，如4G/5G。未来的远程ID系统无疑将基于
这两种方法，而不仅仅是一种方法，因为这两种方式是
互补的[16]，[60]。例如，ScaleFlytRemoteID提供直接
广播和网络信道（符合ASTM和ASD-STAN），并在防
篡改嵌入式eSIM中提供基于密码学的安全通信信道[61]。

\subsubsection{广播技术}

广播远程ID仅由单向数据传输组成，没有特定的目的
地或接收者。广播范围内的任何一个都可以接收数据。
广泛的远程身份识别技术在网络覆盖有限、中断或不可
用的地区很有帮助。在远距离上保持广播和接收器之间
的信号可能是不可能的，或者可能需要太多的接收器才
能成为可行的解决方案[62]。

重要的航空电子公司于1990年创建了ADS-B，这是最
理想、最有前景的监视跟踪系统之一。它为商用和军用
飞机提供了更新的通信方案，扩展了通信范围，支持半
径高达370公里[63]。ADS-B允许以最小的延迟自动广
播包含从全球导航卫星系统（GNSS）获得的位置和机
载系统向其他无人机和地面基站提供的其他信息的消息
[63]，[64]。ADS-B是美国下一代航空运输系统的一个
组成部分，无人机可用于探测国家空域中的其他飞机
[65]。ADS-B有两种操作模式，称为ADS-B In和ADS-B
Out，如图10所示。无人机的位置、识别和速度
以第一模式定期发送到地面站，而不需要任何外部动作。
在第二种模式下，无人机相互交换此类信息，主要涉及
飞行信息服务-广播（FIS-B）、交通信息服务广播
（TIS-B）数据和其他ADS-B消息[63]。

现有的ADS-B模块将消息广播到两个主要频率，即
1090 MHz和978 MHz[66]。978 MHz仅在美国用于测试
目的，被称为通用接入收发器（UAT）。许多公司开发
了低功耗和低成本的ADS-B模块，如UAvionics开发的
ping2020、Aerobits设计的TT-MC1和Sagetech设计的
DDA。大疆等其他公司将ADS-B模块集成到2020年后
发布的所有无人机中，允许通知附近的任何无人机。不
幸的是，目前还没有配备这种模块的商用无人机，因为
这可能会导致不必要的信号流量，尤其是在无人机数量
不断增加的情况下。美国联邦航空管理局报告称，由于
空域中无人机的急剧增加，ADS-B Out将产生过度的信
号饱和，并对载人飞机造成整体安全隐患。此外，
ADS-B不提供有关无人机控制站位置的信息。尽管美国
联邦航空局暗示ADS-B不是识别无人机的合适解决方案，
但许多公司和研究实验室仍专注于开发和增强ADS-B系
统。该增强主要侧重于优化功耗，对于小型电池供电的
无人机来说，功耗过高，因为功耗可能会上升20W[67]。
[68]中提出了一种低功耗ADS-B。所提出的模块的功率
传输小于1.3W，而不是20W，吞吐量降至每三秒一条
消息，这不足以进行安全的冲突管理。此外，由于巨大
的传输功率，ADS-B接收机在高流量条件下可能会出现
信道拥塞，或者被近距离的发射机致盲
（≤50公里）。最后，无人机的ADS-B收发器比小型无
人机贵得多[69]。

基于射频的系统发展过度，是无人机可以用来广播其
ID的有前景的替代方案。无人机必须集成使用正确和未
经许可的射频的收发器，以避免对紧急服务、手机和卫
星等重要无线电系统的有害干扰。最常用的广播频率是
433MHz/2.4GHz，用于远程控制；5.8GHz用于音频和视
频链接[70]。使用的频率应主要遵守每个国家的国家和
国际法规，如功率水平、占空比调制和子信道。射频发
射机连续广播消息，为相关设备通告其存在。这些广告
通常携带有效载荷，并包含广播的远程ID数据。手持设
备不需要建立连接来接收远程ID数据；相反，它只需要
接收和处理
广告[15]。广播RID的最佳射频替代方案是Wi-Fi、蓝牙
和低功耗广域网（LPWAN）[71]。我们将在本节的其余
部分简要介绍这些技术。

\begin{itemize}
  \item Wi-Fi：是一种无线以太网标准，旨在支持局域网，
被称为802.11b。该标准允许设备使用千兆赫范围
内的射频共享信息[71]。Wi-Fi模块是现成的，价格
便宜。与5G/6G模块不同，操作Wi-Fi接入点或Wi
Fi设备不需要许可证。一些Wi-Fi模块的覆盖范围
不超过300米，而其他模块可以达到2公里，如Wi
Fi Neighbor Awareness Networking NAN和Wi-Fi信
标[72]，[73]。Wi-Fi NAN和Wi-Fi信标的功耗为100
mW。据我们所知，目前还没有研究工作在广播
RID的背景下使用Wi-Fi模块。一些工业公司已经证
明了使用Wi-Fi广播RID的可行性，允许用户像大疆
公司一样监视附近的无人机。使用现有的Wi-Fi协
议，信息直接从无人机传输到现成的手机，而无需
完成双向连接。该原型在没有电话覆盖的地区的智
能手机上进行了测试，因为它不需要连接到Wi-Fi
基站或蜂窝网络。此外，智能手机可以接收Wi-Fi
信号来自距离发射无人机一公里多的地方。
  \item 蓝牙：是一种优秀的无线通信标准，因为它提供了
一种安全的连接，同时保持了相对较低的功耗。蓝
牙模块是现成的，价格低廉，无需许可证即可操作。
BLE的足够带宽为270kbps，在新模块中可以达到
650kbps。此技术不适合传输大量数据，RID则不然。
有两种蓝牙版本：蓝牙传统广告（蓝牙4.x）和蓝
牙远程扩展广告（蓝牙5.x）。第一个版本的预期
射程为250米，而第二个版本的射程在理想条件下
可达1公里[73]。这两个模块的最大传输功率约为
10mW。例如，Unifly公司开发了一种BLIP系统，
该系统集成了一个BLE模块来广播飞行和操作员信
息。当局可以通过安全的应用程序在线访问这些数
据。它可以在距离观察者200米以内被捕捉到。广
播期间消耗的能量小于10mW。
  \item 低功耗广域网（LPWAN）：是另一种可以解决RID
广播的替代方案。LPWAN通信技术旨在
它以低数据速率和高延迟为代价提供长距离，并在
未经许可的工业科学和医疗（ISM）频段运行。此
外，它可以以比传统无线技术更低的成本和功耗运
行[74]，[75]。最著名的未经许可的LPWAN通信技
术是LoRa和sigfox。Sigfox技术是第一个在没有网
络连接的情况下监听无限对象广播数据的物联网网
络。Sigfox是一个基于程序的
通信系统，其中所有的网络和计算复杂性都在云中
而不是设备中进行监控。它在未经许可的亚
GHzISM组内传输数据（例如，欧洲为868 MHz，
北美为915 MHz，亚洲为433 MHz）。Sigfox的最
大数据速率为100bps，这使得该技术不适合广播
RID。相反，LoRa技术近年来引起了行业和学术界
的极大关注，因为它可以达到超过100bps的数据速
率
270kbps，提供长距离连接。例如，在[76]中，
Omkar等人通过考虑各种信噪比、干扰条件、扩频
因子、编码率和部署设置，研究了可靠性和覆盖范
围，展示了LoRa技术在广播RID中的可行性。该研
究小组评估了不同场景下LoRa多用户干扰导致的
性能下降量，并量化了不同编码速率和扩频因子下
获得的性能增益。表3总结了可用于广播RID的
LoRa模块的规格。
\end{itemize}

图11显示了以前的RID技术之间的比较。例如，由于
长距离低功耗，LPWAN是广播RID的最佳选择。这种
技术的缺点是数据速率和延迟低，不适合传输大数据。

\subsubsection{网络ID}

网络广播包括向互联网或服务联盟传输数据。客户可
以访问分发的数据以获取无人机ID和跟踪信息[62]。最
著名的网络广播技术是
蜂窝和卫星网络（SATCOM）。蜂窝技术可以通过提
供广域、经济高效和可靠的无线连接为无人机RID服务
[77]。卫星通信是未来用于广泛传播无人机RID的有吸
引力的替代方案，预计它将成为未来通信基础设施不可
或缺的一部分[78]。卫星通信提供了显著的延迟和覆盖
区域，而蜂窝网络提供了中等延迟和覆盖范围。卫星通
信技术仅适用于陆基通信服务中断和自然灾害期间的紧
急情况。

此外，目前的卫星通信技术存在传输固有延迟的技术
缺点。据我们所知，直到今天，还没有使用卫星通信实
现无人机RID。相反，学术界和工业界特别关注下一代
蜂窝网络，以支持飞行无人机并提供无限的连接，因为
现有的蜂窝基础设施经过优化，可以为位于地面或靠近
地面的用户设备提供服务。

网络广播模式要求将无人机订阅到移动服务运营商的
网络中，因为它主要依赖于蜂窝网络[79]。图12总结了
在蜂窝网络中注册无人机的不同步骤。用户必须通过向
移动网络运营商（MNO）发送请求以提供有关无人机
的信息来注册嵌入式用户识别模块（eSIM）服务。一
旦信息得到验证，移动网络运营商将与政府电信管理局
（GTA）通信，请求与用户共享的PIN和激活码以完成
注册。用户应安装GTA提供的应用程序来管理无人机连
接。用户在移动应用程序中激活eSIM，并使用激活码
和PIN在车辆中安装配置文件。应用程序将PIN和激活
码安全地发送到GTA，以将用户和车辆信息存储在数据
库中，并将PIN码推入车辆。无人机确认其已准备好接
收用户的其他信息，用户输入车辆的激活码和PIN码。
在这个阶段，无人机和操作员共享秘密PIN和激活码，
使他们能够相互识别。

在本节的其余部分，我们将简要介绍最后的工业网络
RID技术。

ScaleFlyt远程ID是泰雷兹集团推出的一种智能解决方案，用于确保无
人机在地面和空中的操作安全[61]。图13a显示了连接到
无人机的ScaleFlyt RID的图片。它包括一个附加的车载
设备、一个web服务器（基于云的解决方案）和一个在
移动应用程序上实现的后端应用程序。ScaleFlyt允许当
局检测和识别无人机识别号、操作员ID和飞行授权。第
三方，如UTM提供商、无人机飞行员和地面接收器，
可以通过安全的网络接收此类信息
数据通信（LTE）。ScaleFlyt在防篡改嵌入式eSIM中提
供基于密码学的安全通信信道。ScaleFlyt易于使用，其
蜂窝连接是全球性的，与任何空域管理器（UTM/USSP）
兼容。无人机操作员必须使用注册号和二维码进行注册，
配置跟踪器，并将远程ID设备连接到无人机上。

瑞士U-Space Implementation（SUSI）成员开发了
NET-RID，符合欧盟委员会通过的U-Space Regulation
（EU）2021/664[80]。NET-RID通过互联网提供有关这
些操作的信息，并允许无人机操作员轻松地与空域当局、
执法部门、其他运营商和公众共享有关其航班的信息。
与运营商和航班相关的必要信息通过开源平台使用蜂窝
网络共享，该平台确保U-Space服务提供商（USSP）已
从其他USSP获得所有相关数据。NET-RID服务符合
ASTM F3411标准，该标准保护了运营商的隐私。

Unfily公司设计并开发了BLIP作为电子板和无人机跟
踪器[81]。图13b显示了安装在无人机上的BLIP的照片。
BLIP系统提供最大的跟踪精度，同时最大限度地减少延
迟，并将运营商和航班信息安全地存储到Unifly和其他
云服务中。它允许当局和用户使用蓝牙低能耗技术访问
在200米范围内飞行的无人机的详细信息。根据欧洲法
律要求，数据通过LTE无线宽带网络定期传输到UTM骨
干网。

RelmaTech公司最近推出了SIAM解决方案，作为一种
强大的远程识别和交通管理应用程序，以解决确保无人
机安全的关键问题[82]。操作员和无人机在公共数据库
中注册，以跟踪有人驾驶和无人驾驶车辆，每个车辆都
有一个数字标识。除了远程识别无人机外，SIAM还允
许监控禁飞区和向适当的第三方提供实时反馈，并一直
作为主动无人机的实时UTM系统运行。

\subsection{无人机集成雷达}

随着无人机安全操作的远程ID法规的引入，工业无人
机制造商预计将按照全球大多数航空监管机构设定的最
后期限遵守这些准则。否则，从长远来看，不合规的无
人机将被禁止，导致该行业的业务损失。几家主要的无
人机公司正试图通过实施多种策略来应对新提出的监管。
建议对现有型号的当前运行生产线进行最小侵入性修改，
以减轻这种不断变化的风险。当前的平台可能需要固件
更新和附加硬件部署来启用RID功能。为了重新设计这
些平台以实现完整的内置RID，暂停现有的生产线将出
现一种更长的周期设计，无人机制造商可能会发现这种
设计不切实际且成本高昂。然而，对于即将进行的无人
机平台计划修订来说，这不是一个问题。将支持RID的
所需硬件和软件集成到自动驾驶仪或飞行控制器子系统
将是新设计周期的一部分。表4显示了已经兼容远程ID
的无人机以及将安装在不符合远程ID的无人驾驶飞机上
的现有模块。在以下小节中，我们总结了一些大公司在
这一领域的活动。

\subsubsection{大疆}

大疆创新，简称DJI，于2006年在中国广东深圳成立，
是全球领先的无人机制造商之一，占据了全球无人机市
场约三分之二的份额[83]。大疆声称，目前的无人机平
台只需要对其飞行控制器进行软件更新，即可启用RID，
而无需额外的硬件。DJI计划
自2017年以来，通过重新利用其平台内与地面站的当前
无线链路，支持无人机识别解决方案[84]。软件更新在
最初用于视频流的C2链路上实现了广播识别信息。此
链接使用2.4GHz或5GHz的频率，具体取决于平台。不
包含C2或Wi-Fi NAN链接的无人机将需要附加模块来广
播识别信息，如Dronavia Beacon。2020年1月1日之后，
所有新车型都将配备大疆AirSense，该公司将ADS-B技
术用于此类应用。大疆在Mavic Air无人机上实施了基
于Wi-Fi技术的远程识别解决方案。实验结果表明，使
用安装在Android操作系统上的DeDrone drone无人机扫
描仪应用程序可以准确地进行无人机识别。操作员的详
细信息和完成者的信息，如位置、速度和无人机序列号，
都会在手机屏幕上实时报告。

Dedrone DroneScanner仍在原型制作阶段，尚未公开，
但该应用程序的源代码可以在GitHub上找到。该应用程
序可以使用Android Studio轻松编译。它持续扫描和解
码安卓手机的蓝牙、Wi-Fi和Wi-Fi信标信号。如果任何
发射器与Open-DroneID信号的说明符匹配，它会将该发
射器添加到列表中，以在地图上显示无人机的位置和
OpenDroneID数据的详细内容。应用程序读取Android功
能标志以确定支持的技术（BLE 4.0或BLE 5.0）。它符
合ASTM F3411远程ID标准和ASD-STAN prEN4709-002
直接远程ID标准的蓝牙、Wi-Fi NAN和Wi-Fi信标部分。

\subsubsection{PARROT}

Parrot是一家多技术公司，专门制造不同类型的无人
机[85]。Parrot在2021年6月发布FreeFlight 6.7软件后引
入了直接远程识别的支持[86]。DRI按照ASTM标准的
规定实现了Open Drone ID协议的消息结构。广播通过
配备基于Wi-Fi信标技术的鹦鹉平台的Wi-Fi模块传输。
信标帧由蜂窝电话捕获，作为在任何操作客户端设备中
自动应用的标准信道扫描操作的一部分。使用移动应用
程序从信标消息中提取航班信息，该应用程序实时显示
为红线追踪鹦鹉阿纳菲的路径。图14显示了Dedrone无
人机扫描仪应用程序的打印屏幕，其中红线描绘了鹦鹉
Anafi的路径，而飞行信息详细信息则显示在信息屏幕
上。

\subsubsection{YUNEEC}

另一家于1999年在中国江苏成立的著名无人机制造商
是Yuneec International[87]。该公司于2021年5月宣布，
其旗舰无人机H520和H520E支持FAA Remote ID[88]。
Yuneec无人机的现有用户只需使用集成到平台中的
Wi-Fi模块更新无人机固件。更新后的固件使用Wi-Fi模
块来广播无人机的识别信息。

\subsubsection{PIXHAWK}

开源平台也不能免于遵守RID法规，大多数平台都试
图在其系统中使用RID。例如，Pixhawk自动驾驶仪硬
件由PX4和Ardupilot自动驾驶仪软件支持，默认情况下
不集成RID。出于这个原因，已经引入了几种充值软件
来解决这个问题，例如PX4的Auterion SDK[89]。SDK
实现了MAVlink协议，将识别信息传输到地面站，以便
通过互联网将后续数据转发到服务器。Pixhawk还可以
通过Aerobits idME等硬件插件启用RID，该插件使用
BLE技术进行广播RID[90]。另一个硬件模块是cubepilot，
由Pixhawk公司改编，支持uAvionics基于ADS-B IN接收
机的广播RID。

\subsection{RID模块}

如今，大多数现有的无人机都没有集成的远程识别模
块。研究机构正在开发新的远程识别设备，以符合美国
联邦航空管理局的规定，将其整合到当前的无人机中。
例如，Unifly和捷克公司提供了两个远程识别模块，可
以改装到市场上所有现有的无人机上。这两个模块广播
所需的
信息，如操作员ID、无人机识别、位置等。Unifly模块
支持4.0蓝牙技术，而捷克模块可以同时使用BLE 4.0和
BLE 5.0进行广播。该公司网站上报告了这两个模块的
使用演示，展示了它们的效率。

\section{研究}

尽管迫切需要在最短的时间内实施这项技术，但RID
的研究太少了。本节通过搜索“远程身份验证”
一词来回顾谷歌学者的研究除了这一领域的信息稀缺外，
一些研究很好地解决了远程识别问题，这与其他研究不
同。因此，我们将本节分为两个小节：重点研究，以及可以帮
助识别无人机的RID-UAV联合研究。

\subsection{重点研究}

\subsubsection{安全性}

安全地广播无人机数据是成功创建现代自动化无人机
交通管理（UTM）系统的关键。许多研究关注安全和
隐私问题，包括身份验证威胁以及身份、位置和飞行路
线的泄露，以提高无人机的飞行安全和服务质量[91]。
有两种类型的广播数据：公共数据和有效载荷数据，它
们是机密的，应防止暴露给未经授权的实体。以前的工
作是由
我们的团队将降低与无人机飞行相关的风险，并区分合
作和不合作的无人机[92]。建议的系统是一种集成到
UTM系统中的反无人机技术，允许使用一套澄清协议
进行信息交换和协调，以对发现的无人机做出负责任的
反应。该架构允许CUAS系统获取有关被发现的无人机
的注册状态以及是否有权执行观察到的飞行的信息。该
系统包含两个数据库：注册无人机身份数据库（ID-DB）
和授权任务数据库（AUTH-DB），其中包含有关无人
机注册和任务授权的最新信息。使用多架无人机对
CUAS系统进行仿真，分析其在不同场景下的性能。

在[93]中，Tedeschi等人提出了无人机的匿名远程识
别（ARID），以实现无人机的远程ID投诉匿名远程识
别。建议的解决方案允许使用临时假名广播消息，只有
受信任的机构才能链接到消息的长期标识符
无人机及其操作员。此外，ARID强制执行消息真实性，
以保护无人机免受假冒和虚假报告的侵害，并在可信机
构上产生的开销可以忽略不计。ARID在3DR无人驾驶
飞机上实现并验证。实验结果表明，最苛刻的配置只需
要大约11.23毫秒来生成消息，消耗4.72毫焦耳的能量。

在[94]中，提出了一种分散的UTM协议来控制空域访
问，以确保空域操作的高度完整性、可用性和机密性。
建议的系统主要解决了管理当局、服务提供商和最终用
户之间安全交互的协议缺乏明确定义的问题。建议的解
决方案基于区块链、智能合约技术和移动人群感应
（MCS）机制，以无缝执行管理无人机操作的空域规则
和规定。该架构与以太坊平台集成，并使用四种创新的
合约验证工具Osiris、Slither、Oyente和SmartCheck进行
验证。仿真结果表明了该代码对多种威胁的鲁棒性，如
中间人攻击、拒绝服务攻击和重放攻击。

Hashem等人[95]提出了一种基于Hyperledger Iroha区
块链的新型无人机ID架构，这是一种块序列。管理员将
新的无人机注册到网络中，并存储公钥和证书。如果互
联网可用，无人机将更新的数据直接广播到区块链。否
则，无人机将数据发送到连接的地面控制站，地面控制
站将代表无人机转发接收到的信息。观察者通过蓝牙和
Wi-Fi广播接收消息或轮询区块链，他们可以获取与无
人机相关的公钥来验证接收到的消息。作者展示了其通
过无人机远程识别协议实现的概念验证，以及该系统对
各种攻击的无懈可击性。

在[96]中，薛平等人提出了基于区块链的无人机系统
架构DroneChain，以确保数据收集和传输过程中的无人
机通信安全，并保持收集数据的完整性。DroneChain包
括四个主要模块：无人机、控制系统、云服务器和区块
链网络。无人机将数据构建为元组：DeviceID、时间、
位置、数据，并将其发送给控制器，控制器将数据转发
到网络模块。后者将对数据进行哈希运算，并将其转换
为Merkle树节点。由于每条记录都会立即存储在云中，
因此可以随时验证数据完整性。

尽管ADS-B是广播RID的最佳和有前景的解决方案之
一，但该系统不包含安全机制[97]，[98]。ASD-B容易受
到窃听、干扰和欺骗等攻击。Sud-hindra等人提出了一
种密码学方法，以克服与无人机数据的安全性和敏感性
相关的问题[99]。该概念在硬件平台上使用低成本的软
件定义无线电和配备ARM处理器的评估板进行了演示。
所提出的框架基于RTL-SDR库，并集成了对称密钥加
密算法来加密/解密数据。该架构消耗一秒钟来解码一
个ADS-B数据包。在相同的背景下，[97]中的研究人员
通过利用基于FFX加密的一些密码原语，然后将其应用
于空中交通监控场景，提出了一种新的解决方案来解决
ADS-B的安全故障。该解决方案对于拥挤的数据链和资
源受限的航空电子设备来说是轻量级的。它还可以容忍
ADS-B无线广播网络中经常发生的包丢失和故障。

\subsubsection{功率、性能和其他方面}

目前的研究还侧重于远程识别模块的技术增强，如范围、
功耗和比特率。例如，水平广播范围应最大化
（≥1Km）。同样，即使无人机，垂直范围也应大于500
米（可能不会飞得这么高）[100]。另一个设计标准是
保持误报概率小于一个错误/小时
延迟小于3.5秒。此外，RID模块应具有最小的电池尺寸，
使其重量轻，这是最大的挑战之一[101]。在[102]中，
Jae等人提出了一种基于能量收集的无人机识别网络，其
中无人机通过从地面控制站传输的射频信号收集能量，
然后将其识别信息传输到地面接收站（GRS）。时间和
带宽分配平衡了收获的能量和无人机的可实现速率。

国家研发公司的一个研究小组设计并开发了一种由轻
质锂离子电池（3.8V，1670 mAh）供电的小型RID发射
机，使其能够连续播放信息超过7小时[103]。评估通过
三种方法进行：“发射器和评估接收器之间的水平距离”、
“发射器的高度”和“发射器的位置”蓝牙
5.0技术是用于广播RID的无线方法，其中达到的最大距
离约为300米，最大通信成功率为95\%。

Omkar等人提出了一项研究，调查了LoRa广播远程ID
的可靠性和范围[76]。本研究量化了两种不同干扰场景
引起的误码率性能和扩频因素，以探索基于LoRa技术
的RID系统对可靠性和覆盖范围的影响。MATLAB仿真
表明，较低的扩频因子具有有限的范围，但具有更短的
空中时间和更高的比特率的优点。Ghubaish等人利用
LoRa技术提出了一种基于低成本LoRaWAN模块的原型，
用于识别和定位无人机[104]。研究表明，配备传输RID
的LoRaWAN模块的无人机可以使用多个地面站轻松定
位。建议的架构在600m的范围内广播RID，同时仅消耗
1w。值得注意的是，LoRa技术被认为是传输RID的绝佳
候选者，因为它可以在视线（LOS）条件下将信号扩展
长达10公里，只需要几瓦[105]，[106]，并且可以达到60
公里的距离范围，不到5\%
分组丢失率[71]。

在[107]中，Chin等人提出了一种基于低功耗通信模块
LoRa和APRS的ADS-B。这些模块与4G网络结合进行测
试，使用四旋翼在广域内广播跟踪位置和飞行数据，以
检查拟议基础设施在低空的能力。报告的性能令人鼓舞，
并在技术能力水平（TCL3）下以高度可接受的条件进
行了验证。

uFly框架在[108]中提出，以确保定期通信并优化无人
机飞行。它允许管理受飞机ADS-B限制的无人机ADS-B
通信流量。uFly在不同城市的无人机飞行空域内实施和
验证，最终在有效地
管理无人机，保持ADS-B通信正常。

\subsection{RID-UAV联合研究}

在一些研究中，“远程识别”一词并不能反映大多数法
规所定义的无人机识别。然而，一些研究人员用它来指
代在图像中检测和识别无人机。在此背景下，一些研究
使用不同的信息源来识别无人机，如射频信号[109]、
[110]、[111]、[112]，[113]，无人机传感器[114]，[115]，
[116]，声学指纹[117]、[118]、[119]、[120]、[121]等。
这些工作不在本文的范围内，因为它们不符合无人机
RID理念。

其他研究使用相机监控来使用机器学习算法识别无人
机[112]，[122]，[123]。此类研究之所以被考虑，是因
为它们代表了一种有吸引力的远程识别无人机的联合替
代方案。例如，在[124]中进行的研究项目中，提出了
基于视觉RSA SecurID 令牌的远程无人机识别
（ReDroId）。ReDroId通过使用闪光灯对RSA令牌进行
光学调制，实现了一种区分敌我无人机的身份验证方案。
机器学习算法使用视频监控摄像头识别闪烁的灯光。它
将检测到的令牌转换为用于将其转换为二进制代码的二
进制代码，从而可以识别无人机。

\section{讨论}

无人机远程识别确实是公共和空域安全不可或缺的技
术。本文中描述的法规、标准、工业解决方案和研究工
作构建了一个框架，以了解该技术的范围，并为相关的
采用、部署、开发和研究活动提供信息。例如，对制定
相关规则感兴趣的航空当局可以使用这个框架，不仅可
以了解其他国家的法规，还可以了解可用标准的范围以
及支持无缝规则制定过程的技术解决方案的能力。回想
一下，美国联邦航空管理局最初提出的规则制定面临着
行业和用户的抵制，这导致了相当大的变化，并相应地
推迟了最终规则的发布。在本节中，我们将讨论这项技
术的主要机遇和挑战。

\subsection{机会}

RID的主要功能是识别空域中的无人机，并通过提供
有关地面控制站位置的信息将无人机与操作员联系起来。
该功能是在人员和夜间操作无人机的核心要求。除了这
一主要功能外，RID还为高级应用和服务、空域监控和
反无人机系统提供了多种机会。

\subsubsection{安全空域运行的机会}

RID技术可以支持多种应用，如态势感知和飞机分离。
由于缺乏与无人机的通信连接，基于广播的RID无法支
持此类应用。美国联邦航空管理局鼓励（但不是强制）
无人机操作员在可行的情况下配备ADS-B In，以提高交
通意识[22]。另一方面，互联网工程任务组已经认识到
使用远程识别来支持基于互联网的应用程序的相关性，
如视线之外的控制和指挥（C2）以及检测和避免
（DAA）。IETF旨在利用现有的互联网资源（协议、标
准、服务、基础设施和商业模式）来支持此类应用。然
而，并非所有应用程序都要求用户的设备具有互联网连
接。例如，如果设备配备了必要的证书，观察者设备可
以对远程识别消息进行身份验证。

\subsubsection{空域监测的机会}

结合公共移动设备，远程识别技术可以提供一个经济
高效的机会来监控空域。人群的移动设备可以持续感知
RID消息，并将其转发到中央系统进行聚合和评估。这
种解决方案被认为是基于广播的RID的理想选择，因为
它不需要互联网接入点的可用性。然而，这个解决方案
应该通过安全解决方案来增强，以防止人群的虚假报告。
收到的报告数量取决于无人机飞行区域的拥挤程度。例
如，在拥挤的市中心，中央服务器可以接收数百份关于
同一架无人机的报告，这是不必要的，可能会使系统过
载。从这个角度来看，监控系统应该实施一种定期机制，
为每架无人机处理预定数量的报告，以避免超载。

\subsubsection{可靠的反坦克作战机会}

此外，远程识别为反无人机系统提供了可靠决策的机
会。目前，这些系统支持两个主要功能：检测和阻断。
探测技术包括雷达、计算机视觉、声学系统和射频探测
器[125]、[126]。阻截解决方案包括堵塞、抓捕或射击
[127]、[128]。这种反无人机系统的双功能概念适用于
任何被发现的无人机都应被归类为非法的敏感区域。在
城市地区，将城市空域划分为敏感区和不敏感区可能会
有问题。远程识别可以通过允许反无人机系统识别无人
机并区分合法和非法无人机，而不是将每架无人机归类
为不需要的无人机，来缓解这一问题。此外，RID允许
在敏感区域或其附近进行受控无人机操作，而不会受到
反无人机系统的阻止。

\subsection{局限与挑战}

远程识别技术因其概念、设计或实施而存在几个局限
性和挑战。我们将在以下小节中讨论这些问题。

\subsubsection{安全与隐私}

远程识别技术容易受到各种安全攻击。最关键的问题
是恶意无人机在执行非法任务时传输虚假身份的能力。
假身份可以是任意的或伪装的。由于身份数据应该是公
开的，因此通过拦截其他无人机的远程身份信息很容易
获得伪装身份。伪装无人机身份尤其重要，因为它不仅
允许非法操作员在不引起观察者担忧的情况下完成任务。
被拦截无人机的无辜所有者也可能面临起诉，因为违规
行为将与他们的无人机有关。

除了操纵身份外，非法无人机还可以更改远程身份信
息中的其他数据。例如，操作员可以违反批准的任务计
划，并通过操纵远程识别消息中的位置或速度数据来隐
藏这种违规行为。

远程识别需要与手机和平板电脑等手持设备兼容，以
便观察员识别附近的无人机，并向当局报告烦人的无人
机。由于无人机可以高速移动并迅速从现场消失，因此
使用公众可用的系统以最小的延迟发送报告非常重要。
然而，这样的系统可能会为各种攻击打开大门。例如，
恶意观察员可以发送虚假或伪造的报告，对无辜的无人
机操作员造成伤害，或暂时或无限期地扰乱服务。

在大区域，需要多个UTM服务供应商来提供所需的
覆盖范围。在远程识别中，ASTM标准对服务和显示提
供商进行了区分。这些供应商应与UTM生态系统中的其
他无人服务供应商交换信息并进行沟通。这种分布式服
务模型为远程识别数据增加了额外的风险，如拦截、欺
骗和操纵。

此问题快照显示，如果不增强安全性，则无法使用远
程识别。事实上，远程身份数据和报告的认证具有很高
的优先级。

操作员的隐私是无人机识别的另一个挑战。透露任务
数据以及真实的无人机身份和操作员的位置可能会对一
些商业运营商的业务构成风险。因此，应考虑不同的解
决方案，包括加密和匿名化，以应对隐私挑战。这样的
解决方案将阻止公众知道无人机及其操作员的真实身份。
尽管如此，执法等特殊机构的地面观察员将可以获得解
密或去匿名服务，帮助他们找到真正的无人机身份。

\subsubsection{远程识别模糊}

假设一名地面观察员在附近空域发现了一架无人机。
观察者在移动设备上启动RID应用程序。该应用程序在
预期位置显示无人机。观察者能否确定应用程序中显示
的无人机与天空中看到的无人机相同？非法操作员可以
驾驶未注册的无人机执行恶意任务，并使用地面发射器
发送虚假的RID来误导地面观察员。因此，RID有一个
类似于车牌的固有模糊问题。如果没有进一步的信息，
几乎不可能确保车牌属于它所乘坐的车辆。当多架无人
机在附近飞行时，将接收到的RID与有视力的无人机相
关联变得更加复杂，地面观察员收到的RID比有视力的
无人机的数量少。这个模糊问题很难解决。[129]中提
出了一种解决方案，其中授权观察员超越对无人机的控
制。在出现歧义的情况下，观察者会向无人机发送命令
以执行特定的动作。当无人机响应此命令时，它被认为
是合法的。观察员应安全地发送命令，以防止其他无人
机模仿响应。应考虑基于计算机视觉的方法来消除多架
和远程无人机的歧义。这种消歧解决方案需要人类观察
者的参与，这可能容易出错，并且处理的无人机数量有
限，只在观察员附近。

\subsubsection{技术故障和数据准确性}

作为一种技术系统，由于车载RID模块或通信链路中
的永久性或暂时性错误，远程识别可能会失败。此外，
由于影响IMU的干扰或来自GPS模块的错误信息，远程
识别消息中包含的位置数据不准确或错误[100]。技术
故障可能与地面观察员的安全或安保违规行为相混淆，
地面观察员可能会做出错误的决定，例如拦截无人机。
这表明，应使用容错系统方法（如添加冗余模块，如冗
余GPS设备）开发具有高可靠性的远程识别系统。除此
之外，地面观察员在决定阻止无人机之前，应该能够获
得更多关于违规无人机的信息。例如，应在ADS-B的
NextGen中安装双系统，以验证主源的位置。

\subsubsection{范围限制}

RID法规和标准是由支持平板电脑和手机等移动设备
的要求驱动的。由于嵌入式通信技术（包括WLAN和蓝
牙）的能力有限，这带来了重大限制。具体来说，这些
技术的飞行距离比许多或大多数高端无人机的飞行高度
要短。当然，飞行超过400英尺（或法规规定的任何高
度）已经是一种违规行为。然而，恶意用户的兴趣仍然
存在
一开始没有被发现。因此，使RID规范与标准移动设备
的功能保持一致是一个主要问题[100]。

无论选择哪种解决方案来广播所需的信息，人们普遍
希望进行远程识别。为了克服网络连接不足的问题，并
支持偏远地区的RID广播，必须使用高空平台站和卫星
技术，用非地面网络补充现有的基础设施[11]。因此，
6G网络将是广播无人机识别的绝佳替代方案，因为它
被设想为提供集成解决方案，具有在单个调制解调器内
使用地面和卫星通信的能力。最后，射频是广播远程识
别的可靠选择，但如果不进行硬件升级，大多数移动设
备都无法接收。

\subsubsection{能量约束}

另一个需要考虑的挑战是能源消耗。能源不足会导致
广播数据中断，并误导地面观察员将无人机归类为非法。
应考虑为远程识别模块提供独立的能源，以缓解这一问
题。可能的解决方案包括太阳能电池板或通过[102]中
提出的从地面控制站传输的射频信号收集能量。另一个
需要考虑的关键点是通信当局的功率水平规定，如国际
电信联盟（ITU）的FCC 47CFR15和47CFR18，这些规
定因国家而异。例如，西班牙的有效各向同性辐射功率
（EIRP）不应超过10 W，而英国为400 W[130]。

\section{结论}

无人机远程识别无疑将在保护空域免受恶意和鲁莽的
无人机操作以及支持公共安全和隐私方面发挥重要作用。
本研究描述了该领域的当前活动，并提供了不同国家相
关法规、主要标准、工业解决方案和研究的详细信息。
讨论不同的机遇和挑战为这项技术带来了希望，并告知
监管机构、标准化机构、行业和研究人员还有什么要做。
主要教训是，RID是一项对空域安全不可忽视的技术。
对于道路交通安全来说，它和汽车牌照一样重要。然而，
将数字RID与物理无人机相关联是一个重大挑战，应该
使用先进的身份验证和消歧解决方案来克服。

% 书面翻译的参考文献
% 默认使用正文的参考文献样式；
% 如果使用 BibTeX，可以切换为其他兼容 natbib 的 BibTeX 样式。
% \bibliographystyle{unsrtnat}
% \bibliographystyle{IEEEtranN}

% 默认使用正文的参考文献 .bib 数据库；
% 如果使用 BibTeX，可以改为指定数据库，如 \bibliography{ref/refs}。
% \printbibliography

% 书面翻译对应的原文索引
\begin{translation-index}
  \nocite{Belwafi2022}
  \bibliographystyle{unsrtnat}
  \printbibliography
\end{translation-index}

\end{translation}
