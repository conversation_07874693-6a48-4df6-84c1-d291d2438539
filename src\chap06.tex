% !TeX root = ../main.tex

\chapter{实机验证}

至此，协议的设计内容基本完成。
下面，我们选择使用软件无线电平台PlutoSDR在真实物理环境中验证协议的可行性。

软件无线电平台指的是通过软件控制工作频率、系统带宽、调制方式等的无线电设备，其对发射和接收信号的类型具有相当大的灵活性，因此可以作为一个通用的射频收发平台来支持新协议的开发与测试。

PlutoSDR是一款由ADI公司开发的低成本、高性能的软件无线电平台，广泛应用于科研、教学以及业余无线电等领域。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.4\textwidth]{figures/pluto.png}
  \caption{PlutoSDR硬件外观}
\end{figure}

以下是它的部分硬件参数：

\begin{table}[htbp]
  \centering
  \caption{PlutoSDR硬件参数}
  \begin{tabular}{|c|c|}
    \hline
    参数 & 值 \\
    \hline
    架构 & 零中频 \\
    \hline
    射频芯片 & AD9363 \\
    \hline
    数字信号处理器 & Zynq-7010 \\
    \hline
    收发通道数 & $2\times2$ \\
    \hline
    中心频率范围 & 321MHz-3.8GHz \\
    \hline
    最大射频带宽 & 20MHz \\
    \hline
    最高采样率 & 61.44Msps \\
    \hline
    采样深度 & 12bit \\
    \hline
    增益范围 & 0dB-74.5dB \\
    \hline
  \end{tabular}
\end{table}

我们使用两台PlutoSDR设备相距2米进行点对点传输实验，分别使用一台PlutoSDR的TX通道作为发射机，另一台PlutoSDR的RX通道作为接收机。
以下是实验得到的星座图，我们可以观察到同步效果正常。同时，我们对接收到的符号序列进行解码，所得到的消息无误码、信息与发端相同。
这说明协议设计在实际物理环境中是可行的。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/result.png}
  \caption{实机验证得到的星座图}
\end{figure}
