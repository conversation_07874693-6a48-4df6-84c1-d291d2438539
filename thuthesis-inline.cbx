\ProvidesFile{thuthesis-inline.cbx}[2020/08/01 v1.0 ThuThesis biblatex
  citation style]

\RequireCitationStyle{gb7714-2015}

\ExecuteBibliographyOptions{
  autocite = inline ,
}

% 修改 \mkbibsuperscript 宏在最后加上一个空白使得 xeCJK 不会自动在引用标签后面加上中英文之间的空白
\DeclareRobustCommand{\mkbibsuperscriptusp}[1]{%
  \unspace\allowhyphens\textsuperscript{%
    \begingroup
    \protected\long\def\mkbibsuperscriptusp##1{%
      \blx@warning{Nested superscript}%
      \mkbibbrackets{##1}}%
    #1\endgroup}}

% 这里把 gb7714-2015.cbx 中 \parencite 定义借鉴过来
\DeclareCiteCommand{\cite}%[\mkbibbrackets]
  {[\usebibmacro{cite:init}%]
   \usebibmacro{prenote}}%
  {\usebibmacro{citeindex}%
   \usebibmacro{cite:comp}}
  {}
  {%[
  \usebibmacro{cite:dump}]%
   \mkbibsuperscriptusp{\printfield{postnote}}}
