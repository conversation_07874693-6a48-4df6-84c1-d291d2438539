% !TeX root = ../main.tex

\chapter{引言}

\section{背景}

近年来，“低空经济”成为了我国政府大力支持的新兴产业之一。
《政府工作报告》连续两年（2024年\cite{LiQiang2024}和2025年\cite{LiQiang2025}）提出要推动低空经济作为新增长引擎；
2024年3月，工信部等四部门也联合发布了《通用航空装备创新应用实施方案（2024-2030年）》，计划到2030年“通用航空装备全面融入人民生产生活各领域，成为低空经济增长的强大推动力，形成万亿级市场规模”\cite{GongYeHe}。

低空经济，指的是“以各种有人驾驶和无人驾驶航空器的各类低空飞行活动为牵引，辐射带动相关领域融合发展的综合性经济形态”\cite{WangWenZheng2024}。
其中“低空飞行”一般定义为真高\footnote{真高：即飞行器到地面或水面的垂直距离。}在1000米以下的飞行活动，而在这一空域内活动的飞行器主要包括无人机、电动垂直起降飞行器（eVTOL）、直升飞机、小型传统固定翼飞机这四大类。
利用这四大类飞行器，可以催生出城市无人机物流配送、eVTOL空中日常通勤、直升机观光旅游、小型固定翼飞机进行农业作业等一系列新业态，为我国经济发展注入新的活力。

当前，低空飞行器的应用主要集中在封闭区域、指定路线的场景（如景区内的直升机巡游服务、市中心与机场之间的点对点交通运输）。
面向未来的普及和发展，低空飞行器需要逐步向开放空域、个性化路线拓展，单位面积的飞行器个数也将随之增加。
在此过程中，空域的有效管理就成为了亟待解决的问题之一：
在封闭路线、指定路线的场景中，由于飞行器通常归属于同一个运营商，因此可以通过提前规划、飞行时人工协调等方式确保空域的安全、有序；
然而，当来到了开放空域时，由于飞行器的生产商、运营商不尽相同，路线也可能出现交叉、重叠，协调空域中的所有飞行器、使它们保持安全距离、同时减少等待和绕路的情况就成为了一个难题。

幸好，针对这个难题，我们可以参考现有的民用航空空域管理系统。
尽管这个系统相当复杂，其核心可以概括为CNS/ATM系统，其中CNS代表通信（Communication）、导航（Navigation）、监视（Surveillance），是保障空中信息传递的硬件基础设施，而ATM则代表空中交通管理系统（Air Traffic Management），是辅助飞行员和空中交通管制员做出决策的软件系统。\cite{ICAO2002}
硬件设施中，通信和导航不难理解，也容易在低空飞行器上找到相似的解决方案（如WiFi专网用于通信、GPS定位服务用于导航），但监视是什么意思呢？

监视系统的功能是“提供目标（包括空中航空器及机场场面动目标）的实时动态信息”\cite{ZhongGuoMin}，是实现有效空域管理的关键技术之一。
空中交通控制中心依靠监视系统获知飞机的位置、高度、速度等信息，并基于此动态地分配空域、与飞行员协调航路，从而确保空域内飞行器能够安全、有序地运行。
现代民航空中交通管理主要使用一次监视雷达\footnote{一次监视雷达通过接收机身反射的电磁波来测量方位、距离等信息。}、
二次监视雷达\footnote{二次监视雷达在一次监视雷达的基础上使用询问机和应答机的双向通信获得高度、身份识别代码等信息。}
和自动相关监视系统（ADS）作为监视系统，其中广播式自动相关系统（ADS-B）因其运行成本低、可以实现飞行信息全域共享等优势，已然成为空管技术发展和系统建设的重点。

广播式自动相关系统（Automatic Dependent Surveillance - Broadcast System）基于航空器或地面站上安装的ADS-B发射机和接收机。
发射机获取机上传感器数据（如气压高度、空速等）以及GPS定位数据，并每隔一段时间将这些数据在特定的电磁频率上进行公开广播，接收机则监听相同频率来获取空域内所有航空器的发送信息。
ADS-B系统的特点和优势包括\cite{RTCA2011}\cite{ZhongGuoMin2024}
\begin{itemize}
  \item 自动性：发射机无需像二次监视雷达中应答机一样需要飞行员和管制员手动操作，而是全程自动获取飞行信息、定期实现发射。同时，ADS-B的消息更新速率为平均1秒2次，较二次雷达（5到12秒一次）大大缩短，为缩短航空器安全间隔提供了据可靠的数据保障。
  \item 相关性：ADS-B系统依赖于GPS系统提供的高精度定位服务，水平精度也较二次雷达更高\footnote{视GPS系统运行情况，ADS-B系统的水平精度可达到米级，而二次雷达的水平精度在30米以上。}，为空域中更高密度的航空器提供了可能。
  \item 广播性：发射机将信息广播到指定的频道上，且消息的编码和调制方式均标准化，因此不仅是地面站、空域内所有其他航空器也可以接收到发射出的飞行信息。
  \footnote{航空器接收ADS-B消息需要具备ADS-B IN功能，属于ADS-B设备的可选功能。}
  即便没有管制员的指挥，航空器也可以实时地根据附近航空器的位置、速度，调整自己的飞行状态，以避免冲突。
\end{itemize}

ADS-B系统不仅是现代民用航空空中交通管理的关键一环，其设计理念还与低空飞行器的应用场景十分契合。
全自动的发射流程天然适合无专业飞行员的飞行器；
更快的更新速率和更高的定位精度适合小型化的飞行器和障碍物密集的低空环境，也为提升飞行器密度提供了硬件基础；
广播性则允许飞行器实时自动地调整航路避免冲突，甚至可以实现去中心化的空域管理（即没有统一的地面站网络）。
那么，ADS-B系统是否能直接应用于低空飞行器的空域管理，构成其CNS/ATM系统的监视系统呢？

在本文第5章，我们通过仿真测试了ADS-B系统的发射机容量在850左右，与当前最密集的民航机场附近空域的航空器数量相当。
因此，如果低空飞行器也使用这一系统，不仅其自身密度受到限制，大量的低空飞行器在同一个频道进行广播也会与民航客机的发射消息发生冲突，对民航安全构成威胁。

这样一来，一个自然的解决方案便是在ADS-B系统的设计理念（即自动性、相关性、广播性）基础上，改进其调制、编码和组网方式，并在新的频道上构建一个类似的通信系统，以适应低空飞行器的特点与需求。
本文所记录的工作即为在这一方向上的一次探索，希望初步设计与实现这样的通信协议。

\section{现状}

由于本论文的主要工作是新通信协议的设计与实现，因此调研部分着重于搜集是否有已有的通信协议可以满足低空飞行器监控功能的需要，以及当前的政策是否允许新协议和相应通信系统的实现。
需要指出的是，下面的调查内容并不包括“先由飞行器与其操作员进行空—地通信，再由操作员以自动或手动的方式将飞行信息公开到互联网或告知管理部门”这种通信模式，因为这种通信模式涉及不同传输载体的切换，通信链路更脆弱、延时更大，而且需要操作员的额外配合，不能有效地处理非合作的情况。

根据工信部《民用无人驾驶航空器无线电管理暂行办法》的规定，民用无人驾驶航空器可以通过三种途径与外界进行通信：直连通信、通过地面公众移动通信系统、通过卫星通信系统。
其中，直连通信所允许使用的频率包括1430-1444MHz\footnote{1430-1438MHz频段频率专用于警用无人驾驶航空器通信系统或警用直升机，1438-1444MHz频段频率用于其他单位和个人民用无人驾驶航空器通信系统。}、2400-2476MHz、5725-5829MHz。\cite{GongYeHe2023a}
后两个频段（2.4GHz和5.8GHz）同时也是公共场所无线局域网（WLAN）的划定频段。

我国民航局发布的《民用微轻小型无人驾驶航空器运行识别最低性能要求（试行）》则对使用这些频率和通信途径实现的监视系统进行了规定。\cite{ZhongGuoMin2024a}
文中规定了微轻小型无人驾驶航空器运行识别\footnote{运行识别（Remote Identification, RID）与监视系统同义，在无人飞行器系统中多使用该词汇。}的报送信息内容和格式（ASTM F3411-22a），还将运行识别消息的报送方式划分为广播式与网络式。
其中，广播式运行识别指的是“无人驾驶航空器系统
\footnote{无人驾驶航空器系统（Unmanned Aircraft System, UAS）指的是由无人驾驶航空器、控制链路及控制站等设备组件共同组成的系统。}
运行过程中，以不指定对象的方式，通过特定无线电频率和传输协议，实时广播无人驾驶航空器系统的运行识别信息”，对应前面工信部规定的直连通信方式；
而网络式运行识别则指的是“无人驾驶航空器系统运行过程中，与特定网络进行链接，并实时主动提供无人驾驶航空器系统的运行识别信息，终端用户通过网络平台获取识别信息，以实现对无人驾驶航空器系统的运行识别”，这对应前面的通过地面公众移动通信系统和卫星通信系统的通信方式。

对于广播式运行识别，当前（截至2024年底）已有的通信协议包括ADS-B、WiFi、蓝牙、和低功耗广域网（LPWAN）。以下逐一介绍这四种协议的优缺点：\cite{Belwafi2022}

\begin{itemize}
  \item ADS-B：如前所述，ADS-B系统是民用航空空中交通管理中广泛使用的监视系统。
  然而，由于其发射机容量较低，不适用于更高密度的低空飞行器（特别是无人机）应用场景。
  目前市售的低空飞行器中，直升飞机和小型传统固定翼一般配备有ADS-B发射机和接收机，部分无人机上则配有ADS-B接收机，主要用来检测附近空域起降的民航客机、避免碰撞。
  \item WiFi：WiFi是一种无线局域网技术，ASTM F3411-22a标准中提出了利用其控制帧（NAN协议发现帧）作为运行识别消息载体的方案。\cite{ASTM2022}
  其优点在于技术成熟可复用、开发成本较低，同时数据传输带宽较大、可以充分利用法规中的频谱资源。
  而其缺点在于传输距离较短（100至300米）、覆盖范围小，同时由于本身用于无线局域网，受地面的电磁环境干扰可能较大。
  在部分无人机上（如大疆Mavic系列）搭载了基于WiFi控制帧的广播式运行识别设备，不过该技术并未普及。
  \item 蓝牙：蓝牙是一种短距离无线通信技术，ASTM F3411-22a标准中也提出了利用其数据帧作为运行识别消息载体的方案。\cite{ASTM2022}
  与WiFi类似，蓝牙技术成熟可复用、开发成本较低，同时功率更低，然而其传输距离更短（100米以内）、覆盖范围更小，且数据传输带宽较小。
  目前仅部分无人机运行识别升级模块（如Dronetag、Unifly BLIP）使用了蓝牙技术。
  \item 低功耗广域网：低功耗广域网（Low Power Wide Area Network, LPWAN）是一类新兴的无线通信技术，目前主要应用于物联网应用。
  目前已有的LPWAN技术包括LoRa、NB-IoT、Sigfox等，其优势包括运行功耗低、通信距离长、硬件成本低。
  然而一方面，由于相关技术尚未成熟，开发成本较高；另一方面，为了实现低功耗、长距离传输，LPWAN所选用的频率较低，带宽也较窄，因此难以满足高密度的低空飞行器应用场景。
  当前市售的无人机上尚未有搭载LPWAN技术的运行识别设备。
\end{itemize}

对于网络式运行识别，则可以利用所有现有的民用通信设施，例如蜂窝网络（包括LTE和近年来大力建设的5G-A通感一体系统）和卫星通信网络（建设中）。
然而，进行网络式运行识别的前提是需要通过地面基站或卫星连接到互联网。对于人口稀疏区域、或300米以上空域，这一前提往往无法满足。
因此，《民用微轻小型无人驾驶航空器运行识别最低性能要求（试行）》中也指出，轻型、小型无人驾驶航空器系统应当同时通过广播式运行识别与网络式运行识别发送识别信息。微型无人驾驶航空器系统则仅需通过广播式运行识别发送识别信息。
也就是说，广播式运行识别与网络式运行识别是互为补充的，而非彼此替代的关系。

在民用通信系统覆盖程度不佳的区域，低空飞行器仍需依赖广播式运行识别来实现监视功能，保障空域安全有序。
而通过分析现有广播式运行识别通信协议，我们发现上述四项协议仍分别有发射机容量低、传输距离小、带宽小的缺点，不能完全适应低空飞行器的特点和需要，也未得到广泛使用或确定为技术标准。
因此，为了丰富低空飞行器广播式运行识别通信协议的技术选项，本课题探索设计与实现一种发射机容量高、传输距离和带宽适中的通信协议，符合低空经济和低空空域管理技术发展的需要。

\section{课题内容与论文结构}

本课题是在ADS-B系统自动性、相关性、广播性的设计理念基础上，改进其调制、编码和组网方式，从而实现一个新的通信协议。
因此，本课题的设计内容主要包含从下至上的三个层面，依次对应本文的第3、4、5章：

\begin{itemize}
  \item 物理层，包括信号的调制方式、前导码的设计、载波同步和符号同步方法等
  \item 编码层，包括消息报文的内容和格式、信道编码方式等
  \item 网络层，包括时钟同步方法、时隙划分方法、冲突避免机制等
\end{itemize}

需要注意的是，本课题只涉及对通信协议的设计与验证，并不涉及实际通信系统硬件电路的实现或优化。

在每一部分设计内容中，我们将先使用理论分析计算进行初步方案设计，再利用仿真实验来测试其效果，并据此调整设计。
最后，当协议设计通过了仿真验证后，我们将使用软件无线电平台PlutoSDR对其进行实机验证，对应本文第6章内容。
