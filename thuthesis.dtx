% \iffalse meta-comment
%
% Copyright (C) 2005-2025 by Tsinghua University TUNA Association <<EMAIL>>
%
% This work may be distributed and/or modified under the
% conditions of the LaTeX Project Public License, either version 1.3c
% of this license or (at your option) any later version.
% The latest version of this license is in
%    https://www.latex-project.org/lppl.txt
% and version 1.3c or later is part of all distributions of LaTeX
% version 2008 or later.
%
% \fi
%
% \iffalse
%<*driver>
\ProvidesFile{thuthesis.dtx}[2025/03/28 7.6.0 Tsinghua University Thesis Template]
\documentclass{ltxdoc}
\usepackage{dtx-style}

\EnableCrossrefs
\CodelineIndex

\begin{document}
  \DocInput{\jobname.dtx}
\end{document}
%</driver>
% \fi
%
% \DoNotIndex{\newenvironment,\@bsphack,\@empty,\@esphack,\sfcode}
% \DoNotIndex{\addtocounter,\label,\let,\linewidth,\newcounter}
% \DoNotIndex{\noindent,\normalfont,\par,\parskip,\phantomsection}
% \DoNotIndex{\providecommand,\ProvidesPackage,\refstepcounter}
% \DoNotIndex{\RequirePackage,\setcounter,\setlength,\string,\strut}
% \DoNotIndex{\textbackslash,\texttt,\ttfamily,\usepackage}
% \DoNotIndex{\begin,\end,\begingroup,\endgroup,\par,\\}
% \DoNotIndex{\if,\ifx,\ifdim,\ifnum,\ifcase,\else,\or,\fi}
% \DoNotIndex{\let,\def,\xdef,\edef,\newcommand,\renewcommand}
% \DoNotIndex{\expandafter,\csname,\endcsname,\relax,\protect}
% \DoNotIndex{\Huge,\huge,\LARGE,\Large,\large,\normalsize}
% \DoNotIndex{\small,\footnotesize,\scriptsize,\tiny}
% \DoNotIndex{\normalfont,\bfseries,\slshape,\sffamily,\interlinepenalty}
% \DoNotIndex{\textbf,\textit,\textsf,\textsc}
% \DoNotIndex{\hfil,\par,\hskip,\vskip,\vspace,\quad}
% \DoNotIndex{\centering,\raggedright,\ref}
% \DoNotIndex{\c@secnumdepth,\@startsection,\@setfontsize}
% \DoNotIndex{\ ,\@plus,\@minus,\p@,\z@,\@m,\@M,\@ne,\m@ne}
% \DoNotIndex{\@@par,\DeclareOperation,\RequirePackage,\LoadClass}
% \DoNotIndex{\AtBeginDocument,\AtEndDocument}
%
% \GetFileInfo{\jobname.dtx}
%
% \def\indexname{索引}
% \IndexPrologue{\section{\indexname}}
%
% \title{\bfseries\color{violet}\thuthesis：清华大学学位论文模板}
% \author{{\fangsong 清华大学 TUNA 协会}\\[5pt]\texttt{<EMAIL>}}
% \date{v\fileversion\ (\filedate)}
% \maketitle\thispagestyle{empty}
%
%
% \begin{abstract}\noindent
%   此宏包旨在建立一个简单易用的清华大学学位论文模板，包括本科综合论文训练、硕士
%   论文、博士论文以及博士后出站报告。
% \end{abstract}
%
% \vskip2cm
% \def\abstractname{免责声明}
% \begin{abstract}
% \noindent
% \begin{enumerate}
% \item 本模板的发布遵守 \href{https://www.latex-project.org/lppl/lppl-1-3c.txt}{\LaTeX{} Project Public License (1.3.c)}，使用前请认真阅读协议内
%   容。
% \item 本模板为作者根据
%   清华大学研究生院颁发的《
%    \href{https://info2021.tsinghua.edu.cn/f/info/xxfb_fg/xnzx/template/detail?xxid=fa880bdf60102a29fbe3c31f36b76c7e}{%
%   研究生学位论文写作指南}》（更新到 2025 年 3 月版本，限校内网络访问）、
%   英文版 \emph{Guide to Thesis Writing for Graduate Students}、
%   清华大学教务处颁发的《
%   \href{https://info2021.tsinghua.edu.cn/f/info/xxfb_fg/xnzx/template/detail?xxid=b2d1a49d7e48c8e9b0f7e82f033dbb2b}{%
%   清华大学综合论文训练写作规范（试行）}》、
%   外文系的《英语专业本科生综合论文训练》和
%   清华大学《
%   \href{http://postdoctor.tsinghua.edu.cn/info/czxz/1283}{%
%   编写“清华大学博士后研究报告”参考意见}》
%   编写而成，旨在供清华大学毕业生撰写学位论文使用。
% \item 任何个人或组织以本模板为基础进行修改、扩展而生成的新的专用模板，请严格遵
%   守 \LaTeX{} Project Public License 协议。由于违犯协议而引起的任何纠纷争端均与
%   本模板作者无关。
% \end{enumerate}
% \end{abstract}
%
%
% \clearpage
% \pagestyle{fancy}
% \begin{multicols}{2}[
%   \setlength{\columnseprule}{.4pt}
%   \setlength{\columnsep}{18pt}]
%   \tableofcontents
% \end{multicols}
% \clearpage
%
% \section{模板介绍}
% \thuthesis{}（\textbf{T}sing\textbf{h}ua \textbf{U}niversity \LaTeX{}
% \textbf{Thesis} Template）是为了帮助清华大学毕业生撰写毕业论文而编写
% 的 \LaTeX{} 论文模板。
%
% 本文档将尽量完整地介绍模板的使用方法，如有不清楚之处，或者想提出改进建议，
% 可以在 \href{https://github.com/tuna/thuthesis/issues/}{GitHub Issues}
% 参与讨论或提问。
% 有兴趣者都可以参与完善此手册，也非常欢迎对代码的贡献。
%
% \note[注意：]{模板的作用在于减少论文写作过程中格式调整的时间。前提是遵守模板的
% 用法，否则即便用了 \thuthesis{} 也难以保证输出的论文符合学校规范。}
%
% 用户如果遇到 bug，或者发现与学校《写作指南》 的要求不一致，可以尝试以下办法：
% \begin{enumerate}
%   \item 将模板升级到最新，见第~\ref{sec:upgrade} 节；
%   \item 阅读 \href{https://github.com/tuna/thuthesis/wiki/FAQ}{FAQ}；
%   \item 在 GitHub Issues 中按照说明
%     \href{https://github.com/tuna/thuthesis/issues/new?template=bug_report.md}{报告 bug}。
% \end{enumerate}
%
% \section{贡献者}
% \label{sec:contributors}
%
% \thuthesis{} 的开发过程中，主要的维护者包括：
%
% \begin{itemize}
%  \item 薛瑞尼（\githubuser{xueruini}）：最早的开发者，2005 年创建 \thuthesis{} 并长期进行维护工作。
%  \item 赵涛（\githubuser{alick}）：2011-2015 年活跃，较早期阶段的开发者。
%  \item 李泽平（\githubuser{zepinglee}）：2016 年至今活跃，为目前主要维护者。
%  \item 陈晟祺（\githubuser{Harry-Chen}）：2020 年至今活跃，主要负责非开发性事宜。
% \end{itemize}
%
% 同时，也要感谢所有在 GitHub 上提出问题与贡献代码的同学、老师们。
% \thuthesis{} 的持续发展，离不开你们的帮助与支持。
%
% \section{安装}
% \label{sec:installation}
%
% \thuthesis{} 已经包含在主要的 \TeX{} 发行版中，但是通常版本较旧，而且不方便更新。
% 建议从下列途径下载最新版：
% \begin{description}
%  \item[GitHub] \url{https://github.com/tuna/thuthesis}，从 Release 中下载 zip 文件。
%  \item[TUNA 镜像站] \url{https://mirrors.tuna.tsinghua.edu.cn/github-release/tuna/thuthesis/}，也可在首页选择“获取下载链接——应用软件——\thuthesis{}论文模板”。
% \end{description}
%
%
% 模板支持在 TeX Live、MacTeX 和 MiKTeX 平台下进行编译，但要求 2017 年或更新的发行版。
% 当然，尽可能使用最新的版本可以避免 bug。
%
% \subsection{模板的组成}
% 下表列出了 \thuthesis{} 的主要文件及其功能介绍：
%
% \begin{longtable}{l|p{8cm}}
% \toprule
% {\heiti 文件（夹）} & {\heiti 功能描述}\\\midrule
% \endfirsthead
% \midrule
% {\heiti 文件（夹）} & {\heiti 功能描述}\\\midrule
% \endhead
% \endfoot
% \endlastfoot
% thuthesis.ins & \textsc{DocStrip} 驱动文件（开发用） \\
% thuthesis.dtx & \textsc{DocStrip} 源文件（开发用）\\\midrule
% thuthesis.cls & 模板类文件\\
% thuthesis-*.bst & \hologo{BibTeX} 参考文献表样式文件\\
% thuthesis-*.bbx & BibLaTeX 参考文献表样式文件\\
% thuthesis-*.cbx & BibLaTeX 参考文献引用样式文件\\
% tsinghua-name-bachelor.pdf & 校名 logo，本科生封面使用 \\\midrule
% thuthesis-example.tex & 示例文档主文件\\
% ref/ & 示例文档参考文献目录\\
% data/ & 示例文档章节具体内容\\
% figures/ & 示例文档图片路径\\
% thusetup.tex & 示例文档基本配置\\\midrule
% Makefile & Makefile\\
% latexmkrc & latexmk 配置文件 \\
% README.md & Readme\\
% \textbf{thuthesis.pdf} & 用户手册（本文档）\\\bottomrule
% \end{longtable}
%
% 几点说明：
% \begin{itemize}
% \item \file{thuthesis.cls} 可由 \file{thuthesis.ins}
%   和 \file{thuthesis.dtx} 生成，但为了降低新手用户的使用难度，故
%   将 \file{thuthesis.cls} 文件一起发布。
% \item 使用前阅读文档：\file{thuthesis.pdf}。
% \end{itemize}
%
% \subsection{生成模板}
% \label{sec:generate-cls}
% 模板的源文件（\file{thuthesis.dtx}）中包含了大量的注释，需要将注释去掉生成轻量
% 级的 \file{.cls} 文件供 \cs{documentclass} 调用。
%
% \begin{shell}
%   $ xetex thuthesis.ins
% \end{shell}
%
% \note[注意：]{如果没有生成的模板 \file{thuthesis.cls} 文件
%   （跟 \file{thuthesis-example.tex} 同一目录下），
%   \LaTeX{} 在编译时可能找到发行版中较旧版本的 \file{.cls}，从而造成冲突。}
%
% \subsection{编译论文}
% \label{sec:generate-thesis}
% 本节介绍几种常见的生成论文的方法。用户可根据自己的情况选择。
%
% 在撰写论文时，我们\textbf{不推荐}使用原有的 \file{thuthesis-example.tex} 这一名称。
% 建议将其复制一份，改为其他的名字（如 \file{thesis.tex} 或者 \file{main.tex}）。
% 需要注意，如果使用了来自 \file{data} 目录中的 \file{tex} 文件，
% 则重命名主文件后，其顶端的 \texttt{!TeX root} 选项也需要相应修改。
%
% \subsubsection{GNU make}
% \label{sec:make}
% 如果用户可以使用 GNU make 工具，这是最方便的办法。
% 所以 \thuthesis{} 提供了 \file{Makefile}：
% \begin{shell}
%   $ make thesis    # 生成论文示例 thuthesis-example.pdf
%   $ make spine     # 生成书脊 spine.pdf
%   $ make doc       # 生成说明文档 thuthesis.pdf
%   $ make clean     # 清理编译生成的辅助文件
% \end{shell}
%
% 需要注意，如果更改了主文件的名称，则需要修改 \file{Makefile} 顶端的 \texttt{THESIS} 变量定义。
%
% \subsubsection{latexmk}
% \label{sec:latexmk}
% \texttt{latexmk} 命令支持全自动生成 \LaTeX{} 编写的文档，并且支持使用不同的工具
% 链来进行生成，它会自动运行多次工具直到交叉引用都被解决。
% \begin{shell}
%   $ latexmk thuthesis-example.tex  # 生成示例论文 thuthesis-example.pdf
%   $ latexmk spine.tex              # 生成书脊 spine.pdf
%   $ latexmk thuthesis.dtx          # 生成说明文档 thuthesis.pdf
%   $ latexmk -c                     # 清理编译生成的辅助文件
% \end{shell}
% \texttt{latexmk} 的编译过程是通过 \file{latexmkrc} 文件来配置的，如果要进一步了解，
% 可以参考 \pkg{latexmk} 文档。
%
% \subsubsection{\XeLaTeX}
% \label{sec:xelatex}
% 如果用户无法使用以上两种较为方便的编译方法，就只能按照以下复杂的办法手动编译。
%
% 首先，更新模板：
% \begin{shell}
%   $ xetex thuthesis.ins                       # 生成 thuthesis.cls
% \end{shell}
%
% 然后，生成论文以及书脊：
% \begin{shell}
%   $ xelatex thuthesis-example.tex
%   $ bibtex thuthesis-example.aux              # 生成 bbl 文件
%   $ bibtex thuthesis-example-appendix-a.aux   # 附录 A 的的参考文献
%   $ bibtex thuthesis-example-appendix-b.aux   # 附录 B 的的参考文献……
%   $ bibtex thuthesis-example-index-1.aux      # 本科生的书面翻译对应的原文索引
%   $ xelatex thuthesis-example.tex             # 解决引用
%   $ xelatex thuthesis-example.tex             # 生成论文 PDF
%
%   $ xelatex spine.tex                         # 生成书脊 PDF
% \end{shell}
%
% 下面的命令用来生成用户手册：
% \begin{shell}
%   $ xelatex -shell-escape thuthesis.dtx
%   $ makeindex -s gind.ist -o thuthesis.ind thuthesis.idx
%   $ xelatex -shell-escape thuthesis.dtx
%   $ xelatex -shell-escape thuthesis.dtx  # 生成说明文档 thuthesis.pdf
% \end{shell}
%
% \subsection{升级}
% \label{sec:upgrade}
% 如果需要升级 \thuthesis{}，应当从 GitHub 下载最新的版本，
% 将 \file{thuthesis.dtx}，\file{thuthesis.ins}，\file{tsinghua-name-bachelor.pdf} 和
% \file{thuthesis-*.bst} 拷贝至工作目录覆盖相应的文件，然后按照
% 第~\ref{sec:generate-cls} 节的内容生成新的模板和使用说明。
%
% 有时模板可能进行了重要的修改，不兼容已写好的正文内容，用户应按照示例
% 文档重新调整。
%
% \section{使用说明}
% \label{sec:usage}
% 本手册假定用户已经能处理一般的 \LaTeX{} 文档，并对 \hologo{BibTeX} 有一定了解。如果
% 从未接触过 \TeX{} 和 \LaTeX，建议先学习相关的基础知识。
%
% \subsection{示例文件}
% \label{sec:userguide}
%
% 模板核心文件有：\file{thuthesis.cls}，\file{tsinghua-name-bachelor.pdf},
% \file{thuthesis-*.bst}（\hologo{BibTeX}），
% \file{thuthesis-*.bbx} 和 \file{thuthesis-*.cbx}（BibLaTeX），
% 但如果没有示例文档会较难下手，所以推荐从模板自带的示例文档入手。其中包括了论文
% 写作用到的所有命令及其使用方法，只需用自己的内容进行相应替换就可以。对于不清
% 楚的命令可以查阅本手册。下面的例子描述了模板中章节的组织形式，来自于示例文档，
% 具体内容可以参考模板附带的 \file{thuthesis-example.tex} 和 \file{data/}。
%
% \subsection{论文选项}
% \label{sec:option}
%
% \subsubsection{学位}
% \DescribeOption{degree}
% 选择学位，可选：
% \option{bachelor}，\option{master}，\option{doctor}（默认），\option{postdoc}。
% 本节中的 \emph{key-value} 选项只能在文档类的选项中进行设置，
% 不能用于 \cs{thusetup} 命令。
% \begin{latex}
%   % 博士论文
%   \documentclass[degree=doctor]{thuthesis}
% \end{latex}
%
% \subsubsection{学位类型}
% \label{sec:degree-type}
% \DescribeOption{degree-type}
% 定义研究生学位的类型，可选：\option{academic}（默认）、\option{professional}，
% 本科生不受影响。
% \begin{latex}
%   \documentclass[degree=master, degree-type=professional]{thuthesis}
% \end{latex}
%
% \subsubsection{字体配置}
% \label{sec:font-config}
% \DescribeOption{fontset}
% 模板默认会自动根据操作系统配置合适的字体，
% 用户也可以通过 \option{fontset} 时指定使用预设的字库，如：
% \begin{latex}
%   \documentclass[fontset=windows]{thuthesis}
% \end{latex}
% 允许的选项有 \option{windows}、\option{mac}、\option{ubuntu} 和 \option{fandol}，
% 具体使用的字体见表~\ref{tab:fontset}。
% 用户也可以设置为 \option{none} 并自行配置字体。
%
% \begin{table}[htb]
%   \centering
%   \caption{\thuthesis{} 预设的字体}
%   \label{tab:fontset}
%   \begin{tabular}{cccc}
%     \toprule
%     \option{windows} & \option{mac}    & \option{ubuntu} & \option{fandol} \\
%     \midrule
%     Times New Roman  & Times New Roman & TeX Gyre Termes & TeX Gyre Termes \\
%     Arial            & Arial           & TeX Gyre Heros  & TeX Gyre Heros  \\
%     Courier          & Menlo           & TeX Gyre Cursor & TeX Gyre Cursor \\
%     中易宋体         & 华文宋体        & 思源宋体        & Fandol 宋体     \\
%     中易黑体         & 华文黑体        & 思源黑体        & Fandol 黑体     \\
%     中易仿宋         & 华文仿宋        & Fandol 仿宋     & Fandol 仿宋     \\
%     中易楷体         & 华文楷体        & Fandol 楷体     & Fandol 楷体     \\
%     \bottomrule
%   \end{tabular}
% \end{table}
%
% 需要注意，建议用户在提交终版前使用 Windows 平台的字体进行编译。
% 这样中文字体同 Word 模板一致。
%
% 关于字体的配置，
% 详见 \pkg{fontspec}、\pkg{xeCJK}、\pkg{ctex} 等宏包的使用说明和代码。
%
% \DescribeOption{font}
% 配置全文使用的西文字体。所有可选项目为 \option{auto}（默认）、\option{times}、\option{termes}、
% \option{stix}、\option{xits}、\option{libertinus}、\option{newcm}、\option{lm}、
% \option{newtx}、\option{none}。
% 通常来说，用户\textbf{不需要}调整此选项。
%
% \DescribeOption{cjk-font}
% 配置全文使用的中文字体。所有可选项为 \option{auto}（默认）、\option{windows}、\option{windows-local}、
% \option{mac}、\option{mac-word}、\option{noto}、\option{fandol} 和 \option{none}。
% 通常来说，用户\textbf{不需要}调整此选项，模板会自动通过 \option{fontset} 选项选择合适的字体。
%
% \DescribeOption{windows-font-dir}
% 配置搜索 Windows 字体的路径，仅适用于 Overleaf 等不方便全局安装字体的环境。如果此目录下能找到中易宋体，
% 则将自动使用这些字体编译。如有可能，始终建议全局安装相应字体，模板能够自动检测。
%
% \subsection{论文设置}
% 论文的设置可以通过统一命令 \cs{thusetup} 设置 \emph{key=value} 形式完成。
%
% \DescribeMacro{\thusetup}
% \cs{thusetup} 用法与常见 \emph{key=value} 命令相同，如下：
% \begin{latex}
%   \thusetup{
%     key1 = value1,
%     key2 = {a value, with comma},
%   }
%   % 可以多次调用
%   \thusetup{
%     key3 = value3,
%     key1 = value11,  % 覆盖 value1
%   }
% \end{latex}
%
% \note[注意：]{\cs{thusetup} 使用 \pkg{kvsetkeys} 机制，所以配置项之间不能有空行，否则
% 会报错。}
%
% \subsubsection{输出格式}
% \DescribeOption{output}
% 选择输出的格式是打印版还是电子版（用于提交），可选：\option{print}（默认）、\option{electronic}。
% 打印版 \option{print} 自动在单面打印的部分插入空白页（比如封面），并且保证正文第 1 页在右侧。
% 电子版 \option{electronic} 选项会去掉空白页，这是因为一些院系要求提交的电子版不含空白页。
%
% \begin{latex}
%   \thusetup{
%     output = electronic,
%   }
% \end{latex}
%
% 另外本科生要求有 0.2cm 留给装订线的宽度，这只有在打印版中才会生效。
%
%
% \subsubsection{书写语言}
% \DescribeOption{language}
% 在导言区设置 \option{language} 会修改论文的主要语言，如章节标题等。
% 在正文中设置 \option{language} 只修改接下来部分的书写语言，
% 如标点格式、图表名称，但不影响章节标题等。
%
% \begin{latex}
%   \thusetup{
%     language = english,
%   }
% \end{latex}
%
% 论文的一些部分（如英文摘要、本科生的外文调研报告）要求使用特定的语言，
% 模板已经进行配置，并在这些部分结束后自动恢复为主要语言。
%
% 注意，本科生《写作规范》要求“本科生（含国外来华留学本科生）非外语专业论文统一要求
% 用中文书写。”研究生《写作指南》要求“外国人来华留学生可以用英文撰写学位论文，但
% 须采用中文封面”，“除留学生外，学位论文一律须用汉语书写”，用户须提前与导师和院系
% 的审查教师协商使用何种语言书写论文。
%
% \subsubsection{开题报告}
% \DescribeOption{thesis-type}
% 模板还支持本科生、研究生论文开题报告的格式，可以通过设置 \option{thesis-type=proposal} 得到。
%
% 开题报告与学位论文有两点不同：
% \begin{enumerate}
%   \item 封面的信息和格式有区别，尤其是增加了一行“学号”信息，需要通过 \option{student-id} 填写；
%   \item 开题报告不含英文标题页。
% \end{enumerate}
% \begin{latex}
%   \thusetup{
%     thesis-type = proposal,
%     student-id  = {2000310000},
%   }
% \end{latex}
%
% \subsection{封面信息}
% \label{sec:titlepage}
% 封面信息可以通过统一设置命令 \cs{thusetup} 设置 \emph{key=value} 形式完成；
% 带 * 号的键通常是对应的英文。
%
% \subsubsection{论文标题}
% 中英文标题。可以在标题内部使用换行|\\|。
% \begin{latex}
%   \thusetup{
%     title  = {论文中文题目},
%     title* = {Thesis English Title},
%   }
% \end{latex}
%
% \subsubsection{申请学位名称}
% \label{sec:degree-category}
% 学位名称的设置比较复杂，见表~\ref{tab:degree-category}。
%
% \begin{table}[h]
%   \caption{学位名称的要求}
%   \label{tab:degree-category}
%   \begin{tabular}{p{2cm}p{6cm}p{6cm}}
%     \toprule
%     学位类型 & degree-category & degree-category* \\
%     \midrule
%     学术型博士 & 需注明所属的学科门类，例如：
%         哲学、经济学、法学、教育学、文学、历史学、理学、工学、农学、医学、
%         军事学、管理学、艺术学
%       & Doctor of Philosophy \\
%     \midrule
%     学术型硕士 & 同上
%       & 哲学、文学、历史学、法学、教育学、艺术学门类
%         填写“Master of Arts“，其它填写“Master of Science” \\
%     \midrule
%     专业型研究生学位 & 专业学位的名称，例如：教育博士、工程硕士
%       & 专业学位的名称，例如：Doctor of Education, Master of Engineering \\
%     \midrule
%     本科生 & - & - \\
%     \bottomrule
%   \end{tabular}
% \end{table}
%
% \begin{latex}
%   \thusetup{
%     degree-category  = {工学硕士},
%     degree-category* = {Master of Science},
%   }
% \end{latex}
%
% \subsubsection{院系名称}
% 院系名称。
% \begin{latex}
%   \thusetup{
%     department = {计算机科学与技术系},
%   }
% \end{latex}
%
% \subsubsection{学科名称}
%
% \begin{itemize}
%   \item 研究生学术型学位：获得一级学科授权的学科填写一级学科名称，其他填写二级学科名称；
%   \item 本科生：专业名称，第二学位论文需标注“（第二学位）”。
% \end{itemize}
%
% \begin{latex}
%   \thusetup{
%     discipline  = {学科名称},
%     discipline* = {Discipline in English},
%   }
% \end{latex}
%
% \subsubsection{专业领域}
%
% 仅用于研究生专业型学位。
%
% \begin{itemize}
%   \item 设置专业领域的专业学位类别，填写相应专业领域名称；
%   \item 2019 级及之前工程硕士学位论文，在 \option{engineering-field} 填写相应工程领域名称；
%   \item 其他专业学位类别的学位论文无需此信息。
% \end{itemize}
%
% \begin{latex}
%   \thusetup{
%     professional-field  = {计算机技术},
%     professional-field* = {Computer Technology},
%   }
% \end{latex}
%
%
% \subsubsection{作者姓名}
% 作者姓名。
% \begin{latex}
%   \thusetup{
%     author  = {中文姓名},
%     author* = {Name in Pinyin},
%   }
% \end{latex}
%
% \subsubsection{学号}
% 学号，仅用于本、研论文开题报告。
% \begin{latex}
%   \thusetup{
%     student-id  = {20000310000},
%   }
% \end{latex}
%
% \subsubsection{导师}
% \myentry{导师}
% 导师的姓名与职称之间以“,”（西文逗号，U+002C）隔开，下同。
% \begin{latex}
%   \thusetup{
%     supervisor  = {导师姓名, 教授},
%     supervisor* = {Professor Supervisor Name},
%   }
% \end{latex}
%
% \myentry{副导师}
% 本科生的辅导教师，硕士的副指导教师。
% \begin{latex}
%   \thusetup{
%     associate-supervisor  = {副导师姓名, 副教授},
%     associate-supervisor* = {Professor Assoc-Supervisor Name},
%   }
% \end{latex}
%
% \myentry{联合指导教师}
% \begin{latex}
%   \thusetup{
%     co-supervisor  = {联合指导教师姓名, 教授},
%     co-supervisor* = {Professor Join-Supervisor Name},
%   }
% \end{latex}
%
% \subsubsection{成文日期}
% 默认为当前日期，也可以自己指定，要求使用 ISO 格式。
% \begin{latex}
%   \thusetup{
%     date = {2011-07-01},
%   }
% \end{latex}
%
% \subsubsection{密级}
% \label{sec:setup-secret}
% 定义秘密级别和年限。如果定义了 \option{secret-level}，
% 则会在封面上显示对应密级，并且从声明页中将移除“不包含涉及国家秘密的内容”字样（2025年3月写作指南更新有此要求）。
% \begin{latex}
%   \thusetup{
%     secret-year  = 10,
%     secret-level = {秘密},
%   }
% \end{latex}
%
% \subsubsection{博士后专用参数}
% \begin{latex}
%   \thusetup{
%     clc                = {分类号},
%     udc                = {udc},
%     id                 = {id},
%     discipline-level-1 = {流动站（一级学科）名称},
%     discipline-level-2 = {专业（二级学科）名称},
%     start-date         = {2011-07-01}, % 研究工作起始时间
%   }
% \end{latex}
%
% \myentry{生成封面}
% \DescribeMacro{\maketitle}
% 生成封面，不含授权说明，摘要等。
% \begin{latex}
%   % 直接生成封面
%   \maketitle
% \end{latex}
%
% \subsection{前言部分}
%
% \subsubsection{指导小组、公开评阅人和答辩委员会名单}
% \myentry{答辩委员会名单}
% \DescribeEnv{committee}
% 学位论文指导小组、公开评阅人和答辩委员会名单可以由 \env{committee} 环境生成，
% 其中的可选参数可以使用 \option{name} 根据是有无指导小组设置合适的标题，比如
% \begin{latex}
%   \begin{committee}[name={学位论文公开评阅人和答辩委员会名单}]
%     ...
%   \end{committee}
% \end{latex}
%
% 答辩委员会名单中的表格使用 LaTeX 生成可能略麻烦，也可以导入 Word 版转成的 PDF 文件，
% \begin{latex}
%   \begin{committee}[file=figures/committee.pdf]
%   \end{committee}
% \end{latex}
%
% \subsubsection{授权说明}
% \myentry{授权说明}
% \DescribeMacro{\copyrightpage}
% 可选参数为扫描得到的 PDF 文件名，例如：
% \begin{latex}
%   % 将签字扫描后授权文件 scan-copyright.pdf 替换原始页面
%   \copyrightpage[file=scan-copyright.pdf]
% \end{latex}
%
% \subsubsection{摘要}
% \myentry{摘要正文}
% \DescribeEnv{abstract}
% \DescribeEnv{abstract*}
%
% 摘要直接在正文中使用 \env{abstract}、\env{abstract*} 环境生成。
%
% \begin{latex}
%   \begin{abstract}
%     摘要请写在这里...
%   \end{abstract}
%
%   \begin{abstract*}
%     Here comes the abstract in English...
%   \end{abstract*}
% \end{latex}
%
% \myentry{关键词}
% 关键词需要使用 \cs{thusetup} 进行设置。关键词之间以\emph{西文逗号}隔开，模板会
% 自动调整为要求的格式。关键词的设置只要在摘要环境结束前即可。
% \begin{latex}
%   \thusetup{
%     keywords  = {关键词 1, 关键词 2},
%     keywords* = {keyword 1, keyword 2},
%   }
% \end{latex}
%
% \subsubsection{目录和索引表}
% 目录、插图、表格、公式和算法等索引命令分别如下，将其插入到期望的位置即可（带*的命令表
% 示对应的索引表不会出现在目录中）：
%
% \DescribeMacro{\tableofcontents}
% \DescribeMacro{\listoffigures}
% \DescribeMacro{\listoftables}
% \DescribeMacro{\listoffiguresandtables}
% \DescribeMacro{\listofalgorithms}
% \begin{longtable}{ll}
% \toprule
%   {\heiti 用途} & {\heiti 命令} \\\midrule
% 目录     & \cs{tableofcontents} \\
% 插图清单 & \cs{listoffigures}   \\
% 附表清单 & \cs{listoftables}    \\
% 插图和附表清单 & \cs{listoffiguresandtables} \\
% 算法清单 & \cs{listofalgorithms} \\
% \bottomrule
% \end{longtable}
%
% \LaTeX{} 默认支持插图和表格索引，是通过 \cs{caption} 命令完成的，因此它们必须出
% 现在浮动环境中，否则不被计数。
%
% 如果不想让某个表格或者图片出现在索引里面，那么请使用命令 \cs{caption*}，这
% 个命令不会给表格编号，也就是出来的只有标题文字而没有“表~xx”，“图~xx”，否则
% 索引里面序号不连续就显得不伦不类，这也是 \LaTeX{} 里星号命令默认的规则。
%
% 如果的确想让其编号，但又不想出现在索引中的话，目前模板暂不支持。
%
% \subsubsection{符号对照表}
% \DescribeEnv{denotation}
% 主要符号表环境，跟 \env{description} 类似，使用方法参见示例文件。带一个可选参数，
% 用来指定符号列的宽度（默认为 2.5cm）。
% \begin{latex}
%   \begin{denotation}
%     \item[E] 能量
%     \item[m] 质量
%     \item[c] 光速
%   \end{denotation}
% \end{latex}
%
% 如果默认符号列的宽度不满意，可以通过参数来调整：
% \begin{latex}
%   \begin{denotation}[1.5cm] % 设置为 1.5cm
%     \item[E] 能量
%     \item[m] 质量
%     \item[c] 光速
%   \end{denotation}
% \end{latex}
%
% 符号对照表的另外一种方法是调用 \pkg{nomencl} 宏包，需要在导言区设置：
%
% \begin{latex}
%   \usepackage{nomencl}
%   \makenomenclature
% \end{latex}
%
% 然后在正文中任意位置使用 \cs{nomenclature} 声明需要添加到主要符号表的符号：
%
% \begin{latex}
%   \nomenclature{$m$}{The mass of one angel}
% \end{latex}
%
% 最后使用 \cs{printnomenclature} 命令生成符号表。更详细的使用方法参
% 见 \pkg{nomencl} 宏包的文档。
%
% \subsection{正文部分}
% \subsubsection{图表编号}
% \DescribeOption{figure-number-separator}
% \DescribeOption{table-number-separator}
% \DescribeOption{equation-number-separator}
% 研究生要求图表和公式的编号使用“.”或“-”连接，模板默认使用句点“.”。
% 用户也可以通过 \option{figure-number-separator}、\option{table-number-separator}
% 等选项分别设置：
% \begin{latex}
%   \thusetup{
%     figure-number-separator = {-},
%     table-number-separator = {-},
%     equation-number-separator = {-},
%   }
% \end{latex}
% \DescribeOption{number-separator}
% 也可以使用 \option{number-separator} 同时设置图、表、公式三项的编号连接符，
% 比如 |\thusetup{number-separator = -}|。
%
% 本科生要求“附录中图、表、公式的编号，应与正文中的编号区分开”，
% 应理解为将章号改变为附录对应的大写字母编号，连接符不宜改变。
%
% \subsubsection{数学符号}
% \label{sec:math}
% 中文论文的数学符号默认遵循 GB/T 3102.11—1993《物理科学和技术中使用的数学符号》
% \footnote{原 GB 3102.11—1993，自 2017 年 3 月 23 日起，该标准转为推荐性标准。}。
% 该标准参照采纳 ISO 31-11:1992 \footnote{目前已更新为 ISO 80000-2:2019。}，
% 但是与 \TeX{} 默认的美国数学学会（AMS）的习惯有许多差异。
% 这将在下文详细论述。
%
% \DescribeOption{math-style}
% 用户可以通过设置 \option{math-style} 选择数学符号样式（可选：
% \option{GB}（中文默认），\option{TeX}（英文默认）和 \option{ISO}），比如：
% \begin{latex}
%   \thusetup{
%     math-style = TeX,
%   }
% \end{latex}
%
% 用户也可以逐项修改数学样式，包括：
% \newcommand\dif{\mathop{}\!\mathrm{d}}
% \begin{enumerate}
%   \item \DescribeOption{uppercase-greek}
%     大写希腊字母的正/斜体，可选：\option{italic}、\option{upright}。
%     有限增量符号 $\increment x$ 固定使用正体，推荐使用 \cs{increment} 表示。
%   \item \DescribeOption{less-than-or-equal}
%     小于等于号和大于等于号的字形，可选：\option{slanted}、\option{horizontal}。
%     这将控制 \cs{le}、\cs{ge}、\cs{leq} 和 \cs{geq} 的符号
%     是“$\leqslant$、$\geqslant$”还是“$\leq$、$\geq$”。
%   \item \DescribeOption{integral}
%     积分号的正/斜体，可选：\option{upright}、\option{slanted}。
%     该选项需要字体的支持，目前仅限 \option{xits}、\option{stix}、
%     \option{libertinus} 和 \option{newcm}。参考下文关于数学字体的选择。
%   \item \DescribeOption{integral-limits}
%     积分号上下限的位置，可选：\option{true}（在上下）、\option{false}（在右侧）。
%     这个设置只影响行间公式，行内公式统一居右侧，不受影响。
%   \item \DescribeOption{partial}
%     偏微分符号的正/斜体，可选：\option{upright}、\option{italic}。
%   \item \DescribeOption{math-ellipsis}
%     省略号 \cs{dots} 的样式，可选：\option{centered}（按照中文的习惯固定居中）、
%     \option{lower} 和 \option{AMS}（取决于前后符号的位置）。
%     其他的省略号命令如 \cs{ldots}、\cs{cdots} 则不受影响。
%   \item \DescribeOption{real-part}
%     实部 \cs{Re} 和虚部 \cs{Im} 的字体，可选：\option{roman} 和 \option{fraktur}。
% \end{enumerate}
%
% 如果数学符号选择国标样式 |math-style = GB|，相当于设置了
% \begin{latex}
%   \thusetup{
%     uppercase-greek    = italic,
%     less-than-or-equal = slanted,
%     integral           = upright,
%     integral-limits    = false,
%     partial            = upright,
%     math-ellipsis      = centered,
%     real-part          = roman,
%   }
% \end{latex}
%
% 另外，国标的数学样式与 AMS 还有一些差异无法统一设置，需要用户在写作时进行处理。
% \begin{enumerate}
%   \item 数学常数和特殊函数名用正体，如 $\uppi = 3.14\dots$；$\symup{i}^2 = -1$；
%     $\symup{e} = \lim_{n \to \infty} \left( 1 + \frac{1}{n} \right)^n$。
%   \item 微分号使用正体，比如 $\dif y / \dif x$。
%   \item 向量、矩阵和张量用粗斜体（\cs{symbf}），如 $\symbf{x}$、$\symbf{\Sigma}$、$\symbfsf{T}$。
% \end{enumerate}
%
% 需要注意，上述关于数学符号风格的设置在设置数学字体（\option{math-font}）时才会生效。
%
% \DescribeOption{math-font}
% 模板使用默认使用 XITS Math 作为数学字体。
% 用户也可以使用 \option{math-font} 选项切换其他数学字体，可选：
% \option{stix}（STIX Two Math）、
% \option{libertinus}（Libertinus Math）、
% \option{newcm}（New Computer Modern Math）、
% \option{lm}（Latin Modern Math）。
%
% 其中 \option{lm} 和 \option{newcm} 的字形比较搭配 TeX 原生的 Computer Modern 字体，
% 但与《指南》要求的西文字体 Times New Roman 并不搭配。
% 可能会造成正文和公式中的数字字体不一致，需要谨慎使用。
%
% 以上字体都是 OpenType 格式的字体，需要配合
% \href{http://mirrors.ctan.org/macros/unicodetex/latex/unicode-math/unicode-math.pdf}{\pkg{unicode-math}}
% 宏包使用。
% 全部数学符号的命令参考
% \href{http://mirrors.ctan.org/macros/unicodetex/latex/unicode-math/unimath-symbols.pdf}{\pkg{unimath-symbols}}。
% 注意，\pkg{unicode-math} 宏包与 \pkg{amsfonts}、\pkg{amssymb}、\pkg{bm}、
% \pkg{mathrsfs}、\pkg{upgreek} 等宏包\emph{不}兼容。
% 模板作了处理，用户可以直接使用这些宏包的命令，如 \cs{bm}、\cs{mathscr}、
% \cs{uppi}。
%
% 另外，模板还为 |math-font| 提供了传统的 Type 1 字体 \option{newtx}。
% 该选项会调用 \pkg{newtxmath} 宏包。
% 但是，如果西文字体已经使用了 OpenType 的 Times New Roman，
% 混用 Type 1 字体可能会导致问题，尤其是使用 \pkg{siunitx} 宏包时。
% 该选项还处于测试阶段，需要谨慎使用。
%
% \DescribeOption{eqn-paren-style}
% 控制中文论文中，数学公式编号两边的括号样式。可选项包括：\option{full}（全角，默认） / \option{half}（半角）。
% 在语言为英语（|language = english|）时，此选项无效，仅使用半角括号。
%
% \subsubsection{定理环境}
% \label{sec:theorem}
% \thuthesis{} 定义了常用的数学环境：
%
% \begin{center}
% \begin{tabular}{*{7}{l}}\toprule
%   axiom & theorem & definition & proposition & lemma & conjecture &\\
%   公理 & 定理 & 定义 & 命题 & 引理 & 猜想 &\\\midrule
%   proof & corollary & example & exercise & assumption & remark & problem \\
%   证明 & 推论 & 例子& 练习 & 假设 & 注释 & 问题\\\bottomrule
% \end{tabular}
% \end{center}
%
% 比如：
% \begin{latex}
%   \begin{definition}
%     道千乘之国，敬事而信，节用而爱人，使民以时。
%   \end{definition}
% \end{latex}
% 产生（自动编号）：
% \medskip
%
% \noindent\framebox[\linewidth][l]{{\heiti 定义~1.1~~~} % {道千乘之国，敬事而信，节用而爱人，使民以时。}}
%
% \smallskip
% 列举出来的数学环境毕竟是有限的，如果想用\emph{胡说}这样的数学环境，那么可以定义：
% \begin{latex}
%   \newtheorem{nonsense}{胡说}[chapter]
% \end{latex}
%
% 然后这样使用：
% \begin{latex}
%   \begin{nonsense}
%     契丹武士要来中原夺武林秘笈。—— 慕容博
%   \end{nonsense}
% \end{latex}
%
% 产生（自动编号）：
%
% \medskip
% \noindent\framebox[\linewidth][l]{{\heiti 胡说~1.1~~~} % {契丹武士要来中原夺武林秘笈。—— 慕容博}}
%
% \subsubsection{列表环境}
% \DescribeEnv{itemize}
% \DescribeEnv{enumerate}
% \DescribeEnv{description}
% 为了适合中文习惯，模板将这三个常用的列表环境用 \pkg{enumitem} 进行了纵向间距压
% 缩。一方面清除了多余空间，另一方面用户可以自己指定列表环境的样式（如标签符号，
% 缩进等）。细节请参看 \pkg{enumitem} 文档，此处不再赘述。
%
% \subsubsection{引用方式}
% \label{sec:citestyle}
% 模板支持两种引用方式，分别为理工科常用的“顺序编码制”和文科常用
% 的“著者-出版年制”。
% 使用者在设置参考文献表的格式
% （\cs{bibliographystyle}，见第~\ref{sec:bibliography} 节）时，
% 正文中引用文献的标注会自动调整为对应的格式。
%
% 如果需要标出引文的页码，可以写在 \cs{cite} 的可选参数中，如
% |\cite[42]{knuth84}|。
%
% \paragraph{顺序编码制}
% \DescribeMacro{\inlinecite}
% 顺序编码制的参考文献引用分为两种模式：
% \begin{enumerate}
%   \item 上标模式，比如“同样的工作有很多\textsuperscript{[1-2]}……”；
%   \item 正文模式，比如“文 [3] 中详细说明了……”。
% \end{enumerate}
%
% \DescribeOption{cite-style}
% 用户可以将引用标注的格式设为正文模式：
% \begin{latex}
%   \thusetup{
%     cite-style = inline,
%   }
% \end{latex}
% 也可以使用 \cs{inlinecite}\marg{key} 临时使用正文模式的引用标注。
%
% \paragraph{著者-出版年制}
% 著者-出版年制的参考文献引用有两种模式：
% \begin{enumerate}
%   \item \cs{citep}：著者与年份均在括号中，比如“(Zhang, 2008)”，
%     同默认的 \cs{cite} 命令；
%   \item \cs{citet}：著者姓名作为正文的一部分，比如“Zhang (2008)”；
% \end{enumerate}
%
% 另外，\pkg{natbib} 还提供了其他方便引用的命令，
% 比如 \cs{citeauthor}、\cs{citeyear} 等，
% 更多细节参考 \pkg{natbib} 的文档。
%
% \subsection{其他部分}
%
% \subsubsection{参考文献}
% \label{sec:bibliography}
%
% 参考文献通常可以使用 \hologo{BibTeX} 或 biblatex 生成。
% \hologo{BibTeX} 是 LaTeX 处理参考文献的传统的方式，
% 需要在使用 \cs{bibliographystyle}\marg{style} 选择样式
% 并用 \cs{bibliography} 设置 \file{.bib} 的路径。
% 然后使用 \texttt{bibtex} 对 \file{.aux} 文件进行编译得到 \file{.bbl} 文件。
% 其中的参考文献表内容会在后续编译时替换到 \cs{bibliography} 的位置。
% Biblatex 是较新的方式，需要在载入宏包时通过 \option{style} 选择样式，
% 在导言区使用 \cs{addbibresource} 声明数据库的路径，
% 并在输出参考文献表的位置使用 \cs{printbibliography} 命令,
% 而且编译参考文献的命令需要换为 biber。
% 这两种方式各有优缺点，比如 BibTeX 无法对中文按照拼音排序，一些样式更新不够及时；
% Biblatex 运行较缓慢，无法对多个参考文献表使用不同样式。
% 用户需要根据实际选择合适的方式。
%
% 研究生要求的参考文献格式基于《信息与文献 参考文献著录规则》（GB/T 7714—2015）
% 进行了少量改编（如英文姓名不使用全大写），
% 可以选择“顺序编码制”和“著者-出版年制”。
% 如果使用 BibTeX 的方式，需要在导言区载入 \pkg{natbib} 宏包并选择样式，如：
% \begin{latex}
%   % 顺序编码制
%   \usepackage[sort]{natbib}
%   \bibliographystyle{thuthesis-numeric}
% \end{latex}
% 或
% \begin{latex}
%   % 著者-出版年制
%   \usepackage{natbib}
%   \bibliographystyle{thuthesis-author-year}
% \end{latex}
% 其中的 \option{sort} 选项会将同一处引用的多个文献编号严格按照顺序排序，
% 这并非《写作指南》要求，但是推荐使用。
% 这里调用的样式由 \href{http://ctan.org/pkg/gbt7714}{\pkg{gbt7714}} 的 \file{.bst} 进行了少量修改。
%
% 参考文献表采用“著者-出版年”制组织时，各篇文献首先按文种集中，然后按著者字
% 顺和出版年排列；中文文献可以按著者汉语拼音字顺排列，也可以按著者的笔画笔顺排列。
% 但由于 \hologo{BibTeX} 功能的局限性，无法自动获取著者姓名的拼音或笔画笔顺进行正确排序。
% 一种解决方法是在 \file{.bib} 数据库的中文文献的 |key| 域手动录入著者姓名的拼音，
% 这比较适合中文文献数量较少的情况，如：
% \begin{latex}
%   @book{capital,
%     author = {马克思 and 恩格斯},
%     key    = {ma3 ke4 si1 & en1 ge2 si1},
%     ...
%   }
% \end{latex}
% 另一种方式是使用 biblatex，应在导言区设置
% \begin{latex}
%   \usepackage[style=thuthesis-author-year]{biblatex}
%   \addbibresource{ref/refs.bib}
% \end{latex}
% 这里的样式由 \href{https://ctan.org/pkg/biblatex-gb7714-2015}{biblatex-gb7714-2015} 进行了少量改编，
% 一些额外用法可以参考该宏包的文档。
% 注意 \pkg{biblatex} 跟 \pkg{natbib} 不兼容，
% 而且 \cs{addbibresource} 必须在导言区设置。
% 输出参考文献表应使用 \cs{printbibliography} 命令。
%
% 本科生要求的中文参考文献格式严格遵从 GB/T 7714—2015，
% 附录中调研报告的英文参考文献可以自行选择合适的风格。
% 但是 biblatex 不支持同一文档中使用不同的格式，
% 所以只能使用 \hologo{BibTeX}：
% \begin{latex}
%   % 本科生参考文献的著录格式
%   \usepackage[sort]{natbib}
%   \bibliographystyle{thuthesis-bachelor}
% \end{latex}
% 调研报告的参考文献需要选择与 \pkg{natbib} 兼容的样式。
%
% 本科生外文系要求使用 APA 或 MLA。
% APA 的 BibTeX 样式由 \pkg{apacite} 宏包提供，需要在导言区调用：
% \begin{latex}
%   \usepackage[natbibapa]{apacite}
%   \bibliographystyle{apacite}
% \end{latex}
% 其中 \option{natbibapa} 会调用 \pkg{natbib} 来处理引用，
% 这也是宏包推荐的用法。
% 注意目前的 \pkg{apacite} 只支持到 APA 第 6 版。
% 更推荐使用已经更新到 APA 第 7 版的 \pkg{biblatex-apa}：
% \begin{latex}
%   \usepackage[style=apa]{biblatex}
%   \addbibresource{refs-apa.bib}
% \end{latex}
% 注意，如果参考文献中引用了中文文献的话，这两种方法都不能正确调整格式，
% 需要手动进行修改 \file{.bbl} 文件的内容，
% 这时 BibTeX 比 biblatex 更简单些。
%
% BibTeX 没有用于 MLA 的样式，所以对于 MLA 只能使用 biblatex:
% \begin{latex}
%   \usepackage[style=mla-new]{biblatex}
%   \addbibresource{refs-apa.bib}
% \end{latex}
% 注意这里 \option{mla-new} 对应于 MLA 第 8 版的格式，
% \option{mla} 是第 7 版的。
%
% \subsubsection{致谢}
%
% \DescribeEnv{acknowledgements}
% 把致谢做成一个环境更好一些，直接往里面写感谢的话就可以啦。
%
% \begin{latex}
%   \begin{acknowledgements}
%     …
%     还要特别感谢 \thuthesis{} 节省了论文排版时间！
%   \end{acknowledgements}
% \end{latex}
%
% \subsubsection{声明}
% \DescribeMacro{\statement}
% 直接使用 \cs{statement} 命令可以编译生成声明页。
% 在打印、签字、扫描后如果要插入扫描页，将可选参数 \option{file} 指定为 PDF 文件名，例如：
% \begin{latex}
%   \statement[file=scan-statement.pdf]
% \end{latex}
%
% 由于正文篇幅可能有变化，以及电子版和打印版和有空白页的差别，声明的页码可能不同。
% 所以为了避免重复打印扫描，编译生成声明页时默认\textsf{不含}页眉页脚，
% 插入扫描页时默认加上页眉页脚。
%
% 用户也可以通过 \option{page-style} 参数手动控制声明页是否含页眉页脚。
% 例如编译生成声明页时要求包含页眉页脚：
% \begin{latex}
%   \statement[page-style=plain]
% \end{latex}
% 插入扫描页时不加页眉页脚：
% \begin{latex}
%   \statement[file=scan-statement.pdf, page-style=plain]
% \end{latex}
%
% \subsubsection{附录}
%
% 附录由 \cs{appendix} 命令开启，然后像正文一样书写。
% \begin{latex}
%   \appendix
%   \chapter{...}
%   ...
% \end{latex}
%
% \DescribeOption{toc-depth}
% 一些院系要求目录中只出现附录的章标题，不出现附录中的一级、二级节标题。模板默认
% 如此设置，用户也可以在 \cs{appendix} 命令后手动控制加入目录的标题层级，其
% 中 |0| 表示章标题，|1| 表示一级节标题，以此类推。
%
% \begin{latex}
%   \appendix
%   \thusetup{toc-depth=0}  % 目录只出现章标题
% \end{latex}
%
% \DescribeEnv{survey}
% \DescribeEnv{translation}
% 本科生《写作规范》要求附录 A 为外文资料的调研阅读报告或书面翻译，二者择一。
% 调研报告（或书面翻译）的题目和参考文献是独立于论文的，相当一篇独立的小文章，
% 所以模板相应定义了 \env{survey} 和 \env{translation}。在这两个环境内部可以
% 像论文正文一样使用标题和参考文献的命令，但不会影响外部。
% 但是需要使用 \hologo{BibTeX} 对 \file{*-survey.aux} 或者
% \file{*-translation.aux} 进行编译，才能生成参考文献（见 \ref{sec:xelatex} 节）。
% 如果使用 \texttt{latexmk}，则无需额外处理。
%
% 同时，阅读报告默认切换书写语言为英语，书面翻译默认切换为中文。
% 如有需要，可以通过 \cs{thusetup} 的 \option{language} 参数再次更改。
%
% \begin{latex}
%   \begin{survey}
%     \title{...}
%     \maketitle
%     \tableofcontents
%     ... \cite{...} % 报告内容及其引用
%     \bibliographystyle{...}
%     \bibliography{...} % 报告的参考文献
%   \end{survey}
% \end{latex}
%
% “书面翻译对应的原文索引”区别于译文的参考文献，需要使用 \env{translation-index}
% 环境，另外需要使用 \hologo{BibTeX} 编译 \file{*-index.aux}，\texttt{latexmk} 同样会自动处理。
%
% \begin{latex}
%   \begin{translation}
%     ... \cite{...} % 书面翻译内容及其引用
%     \bibliographystyle{...}
%     \bibliography{...}  % 书面翻译的参考文献
%     \begin{translation-index}
%       \nocite{...}
%       \bibliographystyle{...}
%       \bibliography{...}  % 书面翻译对应的原文索引
%     \end{translation-index}
%   \end{translation}
% \end{latex}
%
% \subsubsection{个人简历、在学期间完成的相关学术成果}
% \DescribeEnv{resume}
% 研究生的标题为“个人简历、在学期间完成的相关学术成果”，
% 本科生的标题为“在学期间参加课题的研究成果”或“PUBLICATIONS”。
%
% \DescribeEnv{achievements}
% 本章的其他标题同样使用 \cs{section*}，\cs{subsection*} 等命令生成，
% 研究成果用 \env{achievements} 环境罗列。
%
% \begin{latex}
%   \begin{resume}
%     \section*{个人简历}
%     ……
%
%     \section*{在学期间完成的相关学术成果}
%
%     \subsection*{学术论文}
%     \begin{achievements}
%       \item ……
%       \item ……
%     \end{achievements}
%
%     \subsection*{专利}
%     \begin{achievements}
%       \item ……
%       \item ……
%     \end{achievements}
%   \end{resume}
% \end{latex}
%
% \subsubsection{综合论文训练记录表}
% \DescribeMacro{\record}
% 本科生需要在最后附上综合论文训练记录表，可以用如下命令：
%
% \begin{latex}
%   \record{file=scan-record.pdf}
% \end{latex}
%
%
% \subsection{书脊}
% \DescribeMacro{\spine}
% \DescribeOption{spine-font}
% \DescribeOption{spine-title}
% \DescribeOption{spine-author}
% 生成装订的书脊，为竖排格式。内容默认使用论文的标题和作者。
% 可以设置 \option{spine-title} 和 \option{spine-author} 来修改。
%
% 博士论文的书脊字体默认为三号字，硕士的为小三号。
% 本科生要求字体大小根据论文的薄厚而定，可以使用 \option{spine-font} 设置字号。
% \begin{latex}
%   \thusetup{
%     spine-font   = {\zihao{3}},
%     spine-title  = {书脊的标题},
%     spine-author = {书脊的作者姓名},
%   }
% \end{latex}
%
% 由于 Fandol 字体在 \XeTeX 中的竖排存在一些问题，如果书脊使用的字体是 Fandol 仿宋
%（\option{fontset} 为 \texttt{fandol} 或者 \texttt{ubuntu} 时），则它\textbf{必须作为独立文件生成}，
% 否则可能导致后续内容文字方向错乱的问题。
%
% \DescribeOption{include-spine}
% 一些院系要求把书脊插进论文里，需要在 \cs{maketitle} 前设置。
% \begin{latex}
%   \thusetup{
%     include-spine = true,
%   }
% \end{latex}
% 打开此选项后，书脊会出现在中文封面后面的第一个空白页。如果有英文封面，则在英文封面之前。
% 如果需要书脊出现在其他位置，请手工使用 \cs{spine} 生成，不要使用此选项。
%
% \section{致谢}
% \label{sec:thanks}
% 感谢这些年来一直陪伴 \thuthesis{} 成长的新老同学！
%
% 欢迎各位到 \href{http://github.com/tuna/thuthesis/}{\thuthesis{} GitHub 主页}贡献！
%
%
% ^^A redefine some commands in markdown package to remove annoying section numbering
% \renewcommand{\markdownRendererHeadingTwo}[1]{\subsection*{#1}}
% \renewcommand{\markdownRendererHeadingThree}[1]{\subsubsection*{#1}}
% ^^A render changelog from markdown
% \markdownInput{CHANGELOG.md}
%
%
% \StopEventually{\PrintIndex}
% \clearpage
%
% \section{实现细节}
%
% \subsection{基本信息}
%    \begin{macrocode}
%<cls>\NeedsTeXFormat{LaTeX2e}[2017/04/15]
%<cls>\ProvidesClass{thuthesis}
%<cls>[2025/03/28 7.6.0 Tsinghua University Thesis Template]
%    \end{macrocode}
%
% 报错
%    \begin{macrocode}
\newcommand\thu@error[1]{%
  \ClassError{thuthesis}{#1}{}%
}
\newcommand\thu@warning[1]{%
  \ClassWarning{thuthesis}{#1}%
}
\newcommand\thu@debug[1]{%
  \typeout{Package thuthesis Info: #1}%
}
\newcommand\thu@patch@error[1]{%
  \thu@error{Failed to patch command \protect#1}%
}
\newcommand\thu@deprecate[2]{%
  \def\thu@@tmp{#2}%
  \thu@warning{%
    The #1 is deprecated%
    \ifx\thu@@tmp\@empty\else
      . Use #2 instead%
    \fi
  }%
}
%    \end{macrocode}
%
% 检查 \LaTeXe{} kernel 版本
%    \begin{macrocode}
\@ifl@t@r\fmtversion{2017/04/15}{}{
  \thu@error{%
    TeX Live 2017 or later version is required to compile this document%
  }
}
%    \end{macrocode}
%
% 检查编译引擎，要求使用 \XeLaTeX。
%    \begin{macrocode}
\RequirePackage{iftex}
\ifXeTeX\else
  \ifLuaTeX\else
    \thu@error{XeLaTeX or LuaLaTeX is required to compile this document}
  \fi
\fi
%    \end{macrocode}
%
% 载入用于测试的配置。
%    \begin{macrocode}
\InputIfFileExists{thuthesis-pdf-test-config.tex}{}{
  \InputIfFileExists{thuthesis-log-test-config.tex}{}{}
}
%    \end{macrocode}
%
% \subsection{定义选项}
% \label{sec:defoption}
% 定义论文类型以及是否涉密
%    \begin{macrocode}
%<*cls>
\hyphenation{Thu-Thesis}
\def\thuthesis{ThuThesis}
\def\version{7.6.0}
\RequirePackage{kvdefinekeys}
\RequirePackage{kvsetkeys}
\RequirePackage{kvoptions}
\SetupKeyvalOptions{
  family=thu,
  prefix=thu@,
  setkeys=\kvsetkeys}
%    \end{macrocode}
%
% \begin{macro}{\thusetup}
% 提供一个 \cs{thusetup} 命令支持 \emph{key-value} 的方式来设置。
%    \begin{macrocode}
\let\thu@setup@hook\@empty
\newcommand\thusetup[1]{%
  \let\thu@setup@hook\@empty
  \kvsetkeys{thu}{#1}%
  \thu@setup@hook
}
%    \end{macrocode}
% \end{macro}
%
% 同时用 \emph{key-value} 的方式来定义这些接口：
% \begin{latex}
%   \thu@define@key{
%     <key> = {
%       name = <name>,
%       choices = {
%         <choice1>,
%         <choice2>,
%       },
%       default = <default>,
%     },
%   }
% \end{latex}
%
% 其中 |choices| 设置允许使用的值，默认为第一个（或者 \meta{default}）；
% \meta{code} 是相应的内容被设置时执行的代码。
%
%    \begin{macrocode}
\newcommand\thu@define@key[1]{%
  \kvsetkeys{thu@key}{#1}%
}
\kv@set@family@handler{thu@key}{%
%    \end{macrocode}
%
% \cs{thusetup} 会将 \meta{value} 存到 \cs{thu@\meta{key}}，
% 但是宏的名字包含 “-” 这样的特殊字符时不方便直接调用，比如 |key = math-style|，
% 这时可以用 |name| 设置 \meta{key} 的别称，比如 |key = math@style|，
% 这样就可以通过 \cs{thu@math@style} 来引用。
% |default| 是定义该 \meta{key} 时默认的值，缺省为空。
%
%    \begin{macrocode}
  \@namedef{thu@#1@@name}{#1}%
  \def\thu@@default{}%
  \def\thu@@choices{}%
  \kv@define@key{thu@value}{name}{%
    \@namedef{thu@#1@@name}{##1}%
  }%
%    \end{macrocode}
%
% 由于在定义接口时，\cs{thu@\meta{key}@@code} 不一定有定义，
% 而且在文档类/宏包中还有可能对该 |key| 的 |code| 进行添加。
% 所以 \cs{thu@\meta{key}@@code} 会检查如果在定义文档类/宏包时则推迟执行，否则立即执行。
%
%    \begin{macrocode}
  \@namedef{thu@#1@@check}{}%
  \@namedef{thu@#1@@code}{}%
%    \end{macrocode}
%
% 保存下 |choices = {}| 定义的内容，在定义 \cs{thu@\meta{name}} 后再执行。
%
%    \begin{macrocode}
  \kv@define@key{thu@value}{choices}{%
    \def\thu@@choices{##1}%
    \@namedef{thu@#1@@reset}{}%
%    \end{macrocode}
%
% \cs{thu@\meta{key}@check} 检查 |value| 是否有效，
% 并设置 \cs{ifthu@\meta{name}@\meta{value}}。
%
%    \begin{macrocode}
    \@namedef{thu@#1@@check}{%
      \@ifundefined{%
        ifthu@\@nameuse{thu@#1@@name}@\@nameuse{thu@\@nameuse{thu@#1@@name}}%
      }{%
        \thu@error{Invalid value "#1 = \@nameuse{thu@\@nameuse{thu@#1@@name}}"}%
      }%
      \@nameuse{thu@#1@@reset}%
      \@nameuse{thu@\@nameuse{thu@#1@@name}@\@nameuse{thu@\@nameuse{thu@#1@@name}}true}%
    }%
  }%
  \kv@define@key{thu@value}{default}{%
    \def\thu@@default{##1}%
  }%
  \kvsetkeys{thu@value}{#2}%
  \@namedef{thu@\@nameuse{thu@#1@@name}}{}%
%    \end{macrocode}
%
% 第一个 \meta{choice} 设为 \meta{default}，
% 并且对每个 \meta{choice} 定义 \cs{ifthu@\meta{name}@\meta{choice}}。
%
%    \begin{macrocode}
  \kv@set@family@handler{thu@choice}{%
    \ifx\thu@@default\@empty
      \def\thu@@default{##1}%
    \fi
    \expandafter\newif\csname ifthu@\@nameuse{thu@#1@@name}@##1\endcsname
    \expandafter\g@addto@macro\csname thu@#1@@reset\endcsname{%
      \@nameuse{thu@\@nameuse{thu@#1@@name}@##1false}%
    }%
  }%
  \kvsetkeys@expandafter{thu@choice}{\thu@@choices}%
%    \end{macrocode}
%
% 将 \meta{default} 赋值到 \cs{thu@\meta{name}}，如果非空则执行相应的代码。
%
%    \begin{macrocode}
  \expandafter\let\csname thu@\@nameuse{thu@#1@@name}\endcsname\thu@@default
  \expandafter\ifx\csname thu@\@nameuse{thu@#1@@name}\endcsname\@empty\else
    \@nameuse{thu@#1@@check}%
  \fi
%    \end{macrocode}
%
% 定义 \cs{thusetup} 接口。
%
%    \begin{macrocode}
  \kv@define@key{thu}{#1}{%
    \@namedef{thu@\@nameuse{thu@#1@@name}}{##1}%
    \@nameuse{thu@#1@@check}%
    \@nameuse{thu@#1@@code}%
  }%
}
%    \end{macrocode}
%
% 定义接口向 |key| 添加 |code|：
%
%    \begin{macrocode}
\newcommand\thu@option@hook[2]{%
  \expandafter\g@addto@macro\csname thu@#1@@code\endcsname{#2}%
}
%    \end{macrocode}
%
%    \begin{macrocode}
\thu@define@key{
  thesis-type = {
    name = thesis@type,
    choices = {
      thesis,
      proposal,
    },
    default = thesis,
  },
  degree = {
    choices = {
      bachelor,
      master,
      doctor,
      postdoc,
    },
    default = doctor,
  },
  degree-type = {
    choices = {
      academic,
      professional,
    },
    name = degree@type,
  },
%    \end{macrocode}
%
% 论文的主要语言。
%    \begin{macrocode}
  main-language = {
    name = main@language,
    choices = {
      chinese,
      english,
    },
  },
%    \end{macrocode}
%
% 用于设置局部语言。
%    \begin{macrocode}
  language = {
    choices = {
      chinese,
      english,
    },
  },
%    \end{macrocode}
%
% 字体
%    \begin{macrocode}
  system = {
    choices = {
      auto,
      mac,
      unix,
      windows,
    },
    default = auto,
  },
  fontset = {
    choices = {
      auto,
      windows,
      mac,
      ubuntu,
      fandol,
      none,
    },
    default = auto,
  },
  font = {
    choices = {
      auto,
      times,
      termes,
      stix,
      xits,
      libertinus,
      newcm,
      lm,
      newtx,
      none,
    },
    default = auto,
  },
  cjk-font = {
    name = cjk@font,
    choices = {
      auto,
      windows,
      windows-local,
      mac,
      mac-word,
      noto,
      fandol,
      none,
    },
    default = auto,
  },
  windows-font-dir = {
    name = windows@font@dir,
    default = {.},
  },
  math-font = {
    name = math@font,
    choices = {
      auto,
      stix,
      xits,
      libertinus,
      newcm,
      lm,
      newtx,
      none,
    },
    default = auto,
  },
  math-style = {
    name = math@style,
    choices = {
      GB,
      ISO,
      TeX,
    },
  },
  uppercase-greek = {
    name = uppercase@greek,
    choices = {
      italic,
      upright,
    },
  },
  less-than-or-equal = {
    name = leq,
    choices = {
      slanted,
      horizontal,
    },
  },
  integral = {
    choices = {
      upright,
      slanted,
    },
  },
  integral-limits = {
    name = integral@limits,
    choices = {
      true,
      false,
    },
  },
  partial = {
    choices = {
      upright,
      italic,
    },
  },
  math-ellipsis = {
    name = math@ellipsis,
    choices = {
      centered,
      lower,
      AMS,
    },
  },
  real-part = {
    name = real@part,
    choices = {
      roman,
      fraktur,
    },
  },
%    \end{macrocode}
%
% 选择打印版还是用于上传的电子版。
%    \begin{macrocode}
  output = {
    choices = {
      print,
      electronic,
    },
    default = print,
  },
%    \end{macrocode}
%
% 数学公式编号的括号使用全角还是半角。
%    \begin{macrocode}
  eqn-paren-style = {
    name = eqn@paren@style,
    choices = {
      full,
      half,
    }
  },
}
\newif\ifthu@degree@graduate
\newcommand\thu@set@graduate{%
  \thu@degree@graduatefalse
  \ifthu@degree@doctor
    \thu@degree@graduatetrue
  \fi
  \ifthu@degree@master
    \thu@degree@graduatetrue
  \fi
}
\thu@set@graduate
\thu@option@hook{degree}{%
  \thu@set@graduate
}
%    \end{macrocode}
%
% 设置默认 \option{openany}。
%    \begin{macrocode}
\DeclareBoolOption[false]{openright}
\DeclareComplementaryOption{openany}{openright}
%    \end{macrocode}
%
% \option{raggedbottom} 选项（默认打开）
%    \begin{macrocode}
\DeclareBoolOption[true]{raggedbottom}
%    \end{macrocode}
%
% 将选项传递给 \pkg{ctexbook}。
%    \begin{macrocode}
\DeclareDefaultOption{\PassOptionsToClass{\CurrentOption}{ctexbook}}
%    \end{macrocode}
%
% 解析用户传递过来的选项，并加载 \pkg{ctexbook}。
%    \begin{macrocode}
\ProcessKeyvalOptions*
%    \end{macrocode}
%
% 设置默认 \option{openany}。
%    \begin{macrocode}
\ifthu@openright
  \PassOptionsToClass{openright}{book}
\else
  \PassOptionsToClass{openany}{book}
\fi
%    \end{macrocode}
%
% \pkg{unicode-math} 和 \pkg{newtx} 都不需要 \pkg{fontspec} 设置数学字体。
%    \begin{macrocode}
\PassOptionsToPackage{no-math}{fontspec}
%    \end{macrocode}
%
% 使用 \pkg{ctexbook} 类，优于调用 \pkg{ctex} 宏包。
%    \begin{macrocode}
\LoadClass[a4paper,UTF8,zihao=-4,scheme=plain,fontset=none]{ctexbook}[2017/04/01]
%    \end{macrocode}
%
%
% \subsection{装载宏包}
% \label{sec:loadpackage}
%
% 引用的宏包和相应的定义。
%    \begin{macrocode}
\RequirePackage{etoolbox}
\RequirePackage{filehook}
\RequirePackage{xparse}
%    \end{macrocode}
%
%    \begin{macrocode}
\RequirePackage{geometry}%
%    \end{macrocode}
%
% 利用 \pkg{fancyhdr} 设置页眉页脚。
%    \begin{macrocode}
\RequirePackage{fancyhdr}
%    \end{macrocode}
%
%    \begin{macrocode}
\RequirePackage{titletoc}
%    \end{macrocode}
%
% 利用 \pkg{notoccite} 避免目录中引用编号混乱。
%    \begin{macrocode}
\RequirePackage{notoccite}
%    \end{macrocode}
%
% \AmSTeX\ 宏包，用来排出更加漂亮的公式。
%    \begin{macrocode}
\RequirePackage{amsmath}
%    \end{macrocode}
%
% 图形支持宏包。
%    \begin{macrocode}
\RequirePackage{graphicx}
%    \end{macrocode}
%
% 并排图形。\pkg{subfigure}、\pkg{subfig} 已经不再推荐，用新的 \pkg{subcaption}。
% 浮动图形和表格标题样式。\pkg{caption2} 已经不推荐使用，采用新的 \pkg{caption}。
%    \begin{macrocode}
\RequirePackage[labelformat=simple]{subcaption}
%    \end{macrocode}
%
% \pkg{pdfpages} 宏包便于我们插入扫描后的授权说明和声明页 PDF 文档。
%
% 由于 \pkg{pdfpages} 跟 \pkg{TikZ} 的 \pkg{external} 库冲突，
% 需要在导言区的结尾进行处理，见
% \href{https://github.com/tuna/thuthesis/issues/693}{\#693}。
%    \begin{macrocode}
\RequirePackage{pdfpages}
\includepdfset{fitpaper=true}
\AtEndPreamble{
  \ifx\tikzifexternalizing\@undefined\else
    \tikzifexternalizing{
      \renewcommand*\includepdf[2][]{}
    }{}
  \fi
}
%    \end{macrocode}
%
% 更好的列表环境。
%    \begin{macrocode}
\RequirePackage[shortlabels]{enumitem}
\RequirePackage{environ}
%    \end{macrocode}
%
% 禁止 \LaTeX{} 自动调整多余的页面底部空白，并保持脚注仍然在底部。
% 脚注按页编号。
%    \begin{macrocode}
\ifthu@raggedbottom
  \RequirePackage[bottom,perpage,hang]{footmisc}
  \raggedbottom
\else
  \RequirePackage[perpage,hang]{footmisc}
\fi
%    \end{macrocode}
%
% 利用 \pkg{xeCJKfntef} 实现汉字的下划线和盒子内两段对齐，并可以避免
% \cs{makebox}\oarg{width}\oarg{s} 可能产生的 underful boxes。
%    \begin{macrocode}
\ifXeTeX
  \RequirePackage{xeCJKfntef}
\else
  \RequirePackage{ulem}
\fi
%    \end{macrocode}
%
% 表格控制
%    \begin{macrocode}
\RequirePackage{array}
%    \end{macrocode}
%
% 使用三线表：\cs{toprule}，\cs{midrule}，\cs{bottomrule}。
%    \begin{macrocode}
\RequirePackage{booktabs}
%    \end{macrocode}
%
%    \begin{macrocode}
\RequirePackage{url}
%    \end{macrocode}
%
% 如果用户在导言区未调用 \pkg{biblatex}，则自动调用 \pkg{natbib}。
%    \begin{macrocode}
\AtEndPreamble{
  \@ifpackageloaded{biblatex}{}{
    \@ifpackageloaded{apacite}{}{
      \RequirePackage{natbib}
    }
  }
}
\AtEndOfPackageFile*{natbib}{
  \@ifpackageloaded{apacite}{}{
    \RequirePackage{bibunits}
  }
}
%    \end{macrocode}
%
% 对冲突的宏包报错。
%    \begin{macrocode}
\newcommand\thu@package@conflict[2]{%
  \AtEndOfPackageFile*{#1}{%
    \AtBeginOfPackageFile*{#2}{%
      \thu@error{The "#2" package is incompatible with "#1"}%
    }%
  }%
}
\thu@package@conflict{biblatex}{bibunits}
\thu@package@conflict{biblatex}{chapterbib}
\thu@package@conflict{biblatex}{cite}
\thu@package@conflict{biblatex}{multibib}
\thu@package@conflict{biblatex}{natbib}

\thu@package@conflict{bibunits}{biblatex}
\thu@package@conflict{bibunits}{chapterbib}
\thu@package@conflict{bibunits}{multibib}

\thu@package@conflict{unicode-math}{amscd}
\thu@package@conflict{unicode-math}{amsfonts}
\thu@package@conflict{unicode-math}{amssymb}
\thu@package@conflict{unicode-math}{bbm}
\thu@package@conflict{unicode-math}{bm}
\thu@package@conflict{unicode-math}{eucal}
\thu@package@conflict{unicode-math}{eufrak}
\thu@package@conflict{unicode-math}{mathrsfs}
\thu@package@conflict{unicode-math}{newtxmath}
\thu@package@conflict{unicode-math}{upgreek}

\thu@package@conflict{natbib}{biblatex}
\thu@package@conflict{natbib}{cite}

\thu@package@conflict{newtxmath}{amsfonts}
\thu@package@conflict{newtxmath}{amssymb}
\thu@package@conflict{newtxmath}{unicode-math}
\thu@package@conflict{newtxmath}{upgreek}
%    \end{macrocode}
%
% \pkg{amsthm} 需要在 \pkg{newtx} 前载入，参考 \pkg{newtx} 的文档。
%    \begin{macrocode}
\AtBeginOfPackageFile*{amsthm}{
  \@ifpackageloaded{newtxmath}{
    \thu@error{The "amsthm" package should be loaded before setting "newtxmath"}
  }{}
}%
%    \end{macrocode}
%
% \subsection{页面设置}
% \label{sec:layout}
%
% 研究生《写作指南》：
% 页边距：上下左右均为 3.0 厘米，装订线 0 厘米；
% 页眉距边界：2.2 厘米，页脚距边界：2.2 厘米。
%
% 本科生《写作规范》：
% 页边距：3 厘米，装订线：0 厘米。
% 本科生 Word 模板：
% 无页眉，页脚距边界：1.5 厘米。
%
% \pkg{fancyhdr} 的页眉是沿底部对齐的，所以只需设置 \cs{headsep}，
% \cs{headheight} 可以适当增加高度允许多行页眉。
% 研究生：\cs{headsep} = $\SI{3}{cm} - \SI{2.2}{cm} - \SI{10.5}{bp} \times 1.3
% \approx \SI{0.3}{cm}$。
%
%    \begin{macrocode}
\geometry{
  paper          = a4paper,  % 210 * 297mm
  marginparwidth = 2cm,
  marginparsep   = 0.5cm,
}
\newcommand\thu@set@geometry{%
  \ifthu@degree@bachelor
    \geometry{
      margin     = 3cm,
      footskip   = 1.5cm,
    }%
  \else
    \geometry{
      margin     = 3cm,
      headheight = 2.7cm,
      headsep    = 0.3cm,
      footskip   = 0.8cm,
    }%
  \fi
}
\thu@set@geometry
\thu@option@hook{degree}{\thu@set@geometry}
\thu@option@hook{output}{\thu@set@geometry}
%    \end{macrocode}
%
%
% \subsection{语言设置}
%
% 定义 \cs{thu@main@language}，当在导言区修改 \option{language} 时，
% 保存为论文的主要语言；
% \cs{thu@reset@main@language} 则用于正文中恢复为主要语言。
%    \begin{macrocode}
\thusetup{main-language=\thu@language}%
\let\thu@main@language\thu@language
\thu@option@hook{language}{%
  \ifx\@begindocumenthook\@undefined\else
    \thusetup{main-language=\thu@language}%
    \let\thu@main@language\thu@language
  \fi
}
\newcommand\thu@reset@main@language{%
  \thusetup{language = \thu@main@language}%
  \let\thu@language\thu@main@language
}
%    \end{macrocode}
%
% 根据语言设置各章节的名称，只有在导言区设置 \option{degree} 和
% \option{language} 时会修改，而在正文局部切换语言时则不变。
%    \begin{macrocode}
\newcommand\thu@set@chapter@names{%
  \ifthu@degree@bachelor
    \def\thu@statement@name{声明}%
  \else
    \def\thu@statement@name{声\hspace{1em}明}%
  \fi
  \ifthu@main@language@chinese
    \def\listfigurename{插图清单}%
    \def\listtablename{附表清单}%
    \def\thu@list@figure@table@name{插图和附表清单}%
    \def\thu@list@algorithm@name{算法清单}%
    \def\thu@denotation@name{符号和缩略语说明}%
    \def\thu@comments@name{指导教师评语}%
    \def\bibname{参考文献}%
    \def\appendixname{附录}%
    \def\indexname{索引}%
    \def\thu@resolution@name{答辩委员会决议书}%
    \ifthu@degree@bachelor
      \def\contentsname{目录}%
      \def\thu@acknowledgements@name{致谢}%
      \def\listequationname{公式索引}%
      \def\thu@resume@name{在学期间参加课题的研究成果}%
    \else
      \def\listequationname{公式清单}%
      \def\thu@acknowledgements@name{致\quad 谢}%
      \ifthu@degree@graduate
        \def\contentsname{目\quad 录}%
        \def\thu@resume@name{个人简历、在学期间完成的相关学术成果}%
      \else  % degree = postdoc
        \def\contentsname{目\qquad 次}%
        \def\thu@denotation@name{符号表}%
        \def\thu@resume@name{个人简历、发表的学术论文与科研成果}%
      \fi
    \fi
  \else
    \ifthu@main@language@english
      \def\thu@comments@name{Comments from Thesis Supervisor}%
      \def\thu@resolution@name{Resolution of Thesis Defense Committee}%
      \def\indexname{Index}%
      \ifthu@degree@bachelor
        \def\contentsname{CONTENTS}%
        \def\listfigurename{FIGURES}%
        \def\listtablename{TABLES}%
        \def\thu@list@figure@table@name{FIGURES AND TABLES}%
        \def\thu@list@algorithm@name{ALGORITHMS}%
        \def\listequationname{EQUATIONS}%
        \def\thu@denotation@name{ABBREVIATIONS}%
        \def\bibname{REFERENCES}%
        \def\appendixname{APPENDIX}%
        \def\thu@acknowledgements@name{ACKNOWLEDGEMENTS}%
        \def\thu@resume@name{PUBLICATIONS}%
      \else
        \def\contentsname{Table of Contents}%
        \def\listfigurename{List of Figures}%
        \def\listtablename{List of Tables}%
        \def\thu@list@figure@table@name{List of Figures and Tables}%
        \def\thu@list@algorithm@name{List of Algorithms}%
        \def\listequationname{List of Equations}%
        \def\thu@denotation@name{List of Symbols and Acronyms}%
        \def\bibname{References}%
        \def\appendixname{Appendix}%
        \def\thu@acknowledgements@name{Acknowledgements}%
        \def\thu@resume@name{Resume}%
      \fi
    \fi
  \fi
}
\thu@set@chapter@names
\thu@option@hook{degree}{\thu@set@chapter@names}
\thu@option@hook{main-language}{\thu@set@chapter@names}
%    \end{macrocode}
%
% 这部分名称在正文中局部地修改语言时会发生变化，比如英文摘要、
% 本科生附录的阅读报告。
%    \begin{macrocode}
\newcommand\thu@set@names{%
  \ifthu@language@chinese
    \ctexset{
      figurename = 图,
      tablename  = 表,
    }%
    \def\thu@algorithm@name{算法}%
    \def\thu@equation@name{公式}%
    \def\thu@assumption@name{假设}%
    \def\thu@definition@name{定义}%
    \def\thu@proposition@name{命题}%
    \def\thu@lemma@name{引理}%
    \def\thu@theorem@name{定理}%
    \def\thu@axiom@name{公理}%
    \def\thu@corollary@name{推论}%
    \def\thu@exercise@name{练习}%
    \def\thu@example@name{例}%
    \def\thu@remark@name{注释}%
    \def\thu@problem@name{问题}%
    \def\thu@conjecture@name{猜想}%
    \def\thu@proof@name{证明}%
    \def\thu@theorem@separator{：}%
  \else
    \ifthu@language@english
      \ctexset{
        figurename = {Figure},
        tablename  = {Table},
      }%
      \def\thu@algorithm@name{Algorithm}%
      \def\thu@equation@name{Equation}%
      \def\thu@assumption@name{Assumption}%
      \def\thu@definition@name{Definition}%
      \def\thu@proposition@name{Proposition}%
      \def\thu@lemma@name{Lemma}%
      \def\thu@theorem@name{Theorem}%
      \def\thu@axiom@name{Axiom}%
      \def\thu@corollary@name{Corollary}%
      \def\thu@exercise@name{Exercise}%
      \def\thu@example@name{Example}%
      \def\thu@remark@name{Remark}%
      \def\thu@problem@name{Problem}%
      \def\thu@conjecture@name{Conjecture}%
      \def\thu@proof@name{Proof}%
      \def\thu@theorem@separator{: }%
    \fi
  \fi
}
\thu@set@names
\thu@option@hook{language}{\thu@set@names}
%    \end{macrocode}
%
% 带圈数字和星号使用中文字体。
%    \begin{macrocode}
\ifLuaTeX
  % ctex 将带圈数字 U+2460–U+24FF 归入字符范围 3（ALchar），这里改回范围 6（JAchar）
  \ltjdefcharrange{3}{%
    "2000-"243F, "2500-"27BF, "2900-"29FF, "2B00-"2BFF}
  \ltjdefcharrange{6}{%
    "2460-"24FF, "2E80-"2EFF, "3000-"30FF, "3190-"319F, "31F0-"4DBF,
    "4E00-"9FFF, "F900-"FAFF, "FE10-"FE1F, "FE30-"FE6F, "FF00-"FFEF,
    "1B000-"1B16F, "1F100-"1F2FF, "20000-"3FFFF, "E0100-"E01EF}
\else
  \ifXeTeX
    \xeCJKDeclareCharClass{CJK}{"2460 -> "2473}
    \xeCJKDeclareCharClass{CJK}{"2605}
  \fi
\fi
%    \end{macrocode}
%
% \newcommand\unicodechar[1]{U+#1（\symbol{"#1}）}
% 由于 Unicode 的一些标点符号是中西文混用的：
% \unicodechar{00B7}、
% \unicodechar{2013}、
% \unicodechar{2014}、
% \unicodechar{2018}、
% \unicodechar{2019}、
% \unicodechar{201C}、
% \unicodechar{201D}、
% \unicodechar{2025}、
% \unicodechar{2026}、
% \unicodechar{2E3A}，
% 所以要根据语言设置正确的字体。
% \footnote{\url{https://github.com/CTeX-org/ctex-kit/issues/389}}
% 此外切换语言时，有一部分名称是需要被重新定义的。
%    \begin{macrocode}
\newcommand\thu@set@punctuations{%
  \ifthu@language@chinese
    \ifLuaTeX
      \ltjsetparameter{jacharrange={+9}}
    \else
      \ifXeTeX
        \xeCJKDeclareCharClass{FullLeft}{"2018, "201C}%
        \xeCJKDeclareCharClass{FullRight}{
          "00B7, "2019, "201D, "2013, "2014, "2025, "2026, "2E3A,
        }%
      \fi
    \fi
  \else
    \ifthu@language@english
      \ifLuaTeX
        \ltjsetparameter{jacharrange={-9}}
      \else
        \ifXeTeX
          \xeCJKDeclareCharClass{HalfLeft}{"2018, "201C}%
          \xeCJKDeclareCharClass{HalfRight}{
            "00B7, "2019, "201D, "2013, "2014, "2025, "2026, "2E3A,
          }%
        \fi
      \fi
    \fi
  \fi
}
\thu@set@punctuations
\thu@option@hook{language}{\thu@set@punctuations}
%    \end{macrocode}
%
% \subsection{字体}
% \label{sec:font}
%
% \subsubsection{字号}
%
% \begin{macro}{\normalsize}
% 正文小四号（12bp）字，行距为固定值 20 bp。
% 其他字号的行距按照相同的比例设置。
%
% 注意重定义 \cs{normalsize} 应在 \pkg{unicode-math} 的 \cs{setmathfont} 前。
%
% 表达式行的行距为单倍行距，段前空 6 磅，段后空 6 磅。
%
% \cs{small} 等其他命令通常用于表格等环境中，这部分要求单倍行距，与正文的字号-行距比例不同，
% 所以保留默认的 1.2 倍字号的行距，作为单倍行距在英文（1.15 倍字号）和中文（1.3倍字号）
% 两种情况的折衷。
%    \begin{macrocode}
\renewcommand\normalsize{%
  \@setfontsize\normalsize{12bp}{20bp}%
  \abovedisplayskip 6bp%
  \abovedisplayshortskip 6bp%
  \belowdisplayshortskip 6bp%
  \belowdisplayskip \abovedisplayskip
}
\normalsize
\ifx\MakeRobust\@undefined \else
    \MakeRobust\normalsize
\fi
%    \end{macrocode}
% \end{macro}
%
% WORD 中的字号对应该关系如下（1bp = 72.27/72 pt）:
% \begin{center}
% \begin{longtable}{llll}
% \toprule
% 初号 & 42bp & 14.82mm & 42.1575pt \\
% 小初 & 36bp & 12.70mm & 36.135 pt \\
% 一号 & 26bp & 9.17mm & 26.0975pt \\
% 小一 & 24bp & 8.47mm & 24.09pt \\
% 二号 & 22bp & 7.76mm & 22.0825pt \\
% 小二 & 18bp & 6.35mm & 18.0675pt \\
% 三号 & 16bp & 5.64mm & 16.06pt \\
% 小三 & 15bp & 5.29mm & 15.05625pt \\
% 四号 & 14bp & 4.94mm & 14.0525pt \\
% 小四 & 12bp & 4.23mm & 12.045pt \\
% 五号 & 10.5bp & 3.70mm & 10.59375pt \\
% 小五 & 9bp & 3.18mm & 9.03375pt \\
% 六号 & 7.5bp & 2.56mm & \\
% 小六 & 6.5bp & 2.29mm & \\
% 七号 & 5.5bp & 1.94mm & \\
% 八号 & 5bp & 1.76mm & \\\bottomrule
% \end{longtable}
% \end{center}
%
% \begin{macro}{\thu@def@fontsize}
% 根据习惯定义字号。用法：
%
% \cs{thu@def@fontsize}\marg{字号名称}\marg{磅数}
%
% 避免了字号选择和行距的紧耦合。所有字号定义时为单倍行距，并提供选项指定行距倍数。
%    \begin{macrocode}
\def\thu@def@fontsize#1#2{%
  \expandafter\newcommand\csname #1\endcsname[1][1.3]{%
    \fontsize{#2}{##1\dimexpr #2}\selectfont}}
%    \end{macrocode}
% \end{macro}
%
% 一组字号定义。
%    \begin{macrocode}
\thu@def@fontsize{chuhao}{42bp}
\thu@def@fontsize{xiaochu}{36bp}
\thu@def@fontsize{yihao}{26bp}
\thu@def@fontsize{xiaoyi}{24bp}
\thu@def@fontsize{erhao}{22bp}
\thu@def@fontsize{xiaoer}{18bp}
\thu@def@fontsize{sanhao}{16bp}
\thu@def@fontsize{xiaosan}{15bp}
\thu@def@fontsize{sihao}{14bp}
\thu@def@fontsize{xiaosi}{12bp}
\thu@def@fontsize{wuhao}{10.5bp}
\thu@def@fontsize{xiaowu}{9bp}
\thu@def@fontsize{liuhao}{7.5bp}
\thu@def@fontsize{xiaoliu}{6.5bp}
\thu@def@fontsize{qihao}{5.5bp}
\thu@def@fontsize{bahao}{5bp}
%    \end{macrocode}
%
% 检测系统。
%    \begin{macrocode}
\ifthu@system@auto
  \IfFileExists{/System/Library/Fonts/Menlo.ttc}{
    \thusetup{system = mac}
  }{
    \IfFileExists{/dev/null}{
      \IfFileExists{null:}{
        \thusetup{system = windows}
      }{
        \thusetup{system = unix}
      }
    }{
      \thusetup{system = windows}
    }
  }
  \thu@debug{Detected system: \thu@system}
\fi
%    \end{macrocode}
%
% 使用 \pkg{fontspec} 配置字体。
%    \begin{macrocode}
\newcommand\thu@mac@word@font@dir{%
  /Applications/Microsoft Word.app/Contents/Resources/DFonts%
}
\ifthu@fontset@auto
  \ifthu@system@windows
    \thusetup{fontset = windows}
  \else
    \IfFontExistsTF{SimSun}{
      \thusetup{fontset = windows}
    }{
      \IfFileExists{\thu@windows@font@dir/Simsun.ttc}{
        \thusetup{fontset = windows, cjk-font = windows-local}
      }{
        \IfFileExists{\thu@mac@word@font@dir/Simsun.ttc}{
          \thusetup{fontset = windows, cjk-font = mac-word}
        }{
          \ifthu@system@mac
            \thusetup{fontset = mac}
          \else
            \IfFontExistsTF{Noto Serif CJK SC}{
              \thusetup{fontset = ubuntu}
            }{
              \thusetup{fontset = fandol}
            }
          \fi
        }
      }
    }
  \fi
  \thu@debug{Detected fontset: \thu@fontset}
\fi
%    \end{macrocode}
%
% \subsubsection{西文字体}
%
% 《指南》要求西文字体使用 Times New Roman 和 Arial，
% 但是在 Linux 下没有这两个字体，所以使用它们的克隆版 TeX Gyre Termes 和
% TeX Gyre Heros。
%    \begin{macrocode}
\newcommand\thu@set@font{%
  \@nameuse{thu@set@font@\thu@font}%
}
\thu@option@hook{font}{\thu@set@font}
%    \end{macrocode}
%
%    \begin{macrocode}
\newcommand\thu@set@font@auto{%
  \ifthu@font@auto
    \ifthu@fontset@windows
      \thusetup{font=times}%
    \else
      \ifthu@fontset@mac
        \thusetup{font=times}%
      \else
        \thusetup{font=termes}%
      \fi
    \fi
  \fi
}
\thu@option@hook{math-font}{\g@addto@macro\thu@setup@hook{\thu@set@font@auto}}
\AtBeginOfPackageFile*{siunitx}{\thu@set@font@auto}
\AtEndPreamble{\thu@set@font@auto}
%    \end{macrocode}
%
% Times New Roman + Arial
%    \begin{macrocode}
\newcommand\thu@set@font@times{%
  \setmainfont{Times New Roman}%
  \setsansfont{Arial}%
  \ifthu@fontset@mac
    \setmonofont{Menlo}[Scale = MatchLowercase]%
  \else
    \setmonofont{Courier New}[Scale = MatchLowercase]%
  \fi
}
%    \end{macrocode}
%
% TeX Gyre Termes
%    \begin{macrocode}
\newcommand\thu@set@font@termes{%
  \setmainfont{texgyretermes}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-italic,
    BoldItalicFont = *-bolditalic,
  ]%
  \thu@set@texgyre@sans@mono
}
\newcommand\thu@set@texgyre@sans@mono{%
  \setsansfont{texgyreheros}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-italic,
    BoldItalicFont = *-bolditalic,
  ]%
  \setmonofont{texgyrecursor}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-italic,
    BoldItalicFont = *-bolditalic,
    Scale          = MatchLowercase,
    Ligatures      = CommonOff,
  ]%
}
%    \end{macrocode}
%
% STIX Two 字体。
% STIX 文件名在 v2.10 2020-12-19 从
% \file{STIX2Text-Regular.otf}、\file{STIX2Math.otf} 分别改为
% \file{STIXTwoText-Regular.otf}、\file{STIXTwoMath-Regular.otf}。
%    \begin{macrocode}
\let\thu@font@family@stix\@empty
\newcommand\thu@set@stix@names{%
  \ifx\thu@font@family@stix\@empty
    \IfFontExistsTF{STIXTwoText-Regular.otf}{%
      \gdef\thu@font@family@stix{STIXTwoText}%
      \gdef\thu@font@name@stix@math{STIXTwoMath-Regular}%
    }{%
      \gdef\thu@font@family@stix{STIX2Text}%
      \gdef\thu@font@name@stix@math{STIX2Math}%
    }%
  \fi
}
\newcommand\thu@set@font@stix{%
  \thu@set@stix@names
  \setmainfont{\thu@font@family@stix}[
    Extension      = .otf,
    UprightFont    = *-Regular,
    BoldFont       = *-Bold,
    ItalicFont     = *-Italic,
    BoldItalicFont = *-BoldItalic,
  ]%
  \thu@set@texgyre@sans@mono
}
%    \end{macrocode}
%
% XITS 字体。
% XITS 的文件名在 v1.109 2018-09-30
% 从 \file{xits-regular.otf}、\file{xits-math.otf} 分别改为
% \file{XITS-Regular.otf}、\file{XITSMath-Regular.otf}。
%    \begin{macrocode}
\let\thu@font@family@xits\@empty
\newcommand\thu@set@xits@names{%
  \ifx\thu@font@family@xits\@empty
    \IfFontExistsTF{XITSMath-Regular.otf}{%
      \gdef\thu@font@family@xits{XITS}%
      \gdef\thu@font@style@xits@rm{Regular}%
      \gdef\thu@font@style@xits@bf{Bold}%
      \gdef\thu@font@style@xits@it{Italic}%
      \gdef\thu@font@style@xits@bfit{BoldItalic}%
      \gdef\thu@font@name@xits@math{XITSMath-Regular}%
    }{%
      \gdef\thu@font@family@xits{xits}%
      \gdef\thu@font@style@xits@rm{regular}%
      \gdef\thu@font@style@xits@bf{bold}%
      \gdef\thu@font@style@xits@it{italic}%
      \gdef\thu@font@style@xits@bfit{bolditalic}%
      \gdef\thu@font@name@xits@math{xits-math}%
    }%
  \fi
}
\newcommand\thu@set@font@xits{%
  \thu@set@xits@names
  \setmainfont{\thu@font@family@xits}[
    Extension      = .otf,
    UprightFont    = *-\thu@font@style@xits@rm,
    BoldFont       = *-\thu@font@style@xits@bf,
    ItalicFont     = *-\thu@font@style@xits@it,
    BoldItalicFont = *-\thu@font@style@xits@bfit,
  ]%
  \thu@set@texgyre@sans@mono
}
%    \end{macrocode}
%
% Libertinus 字体。
% Libertinus 的文件名在 v6.7 2019-04-03 从小写改为驼峰式，
% 在大小写敏感的平台上需要进行判断。
%    \begin{macrocode}
\let\thu@font@family@libertinus\@empty
\newcommand\thu@set@libertinus@names{%
  \ifx\thu@font@family@libertinus\@empty
    \IfFontExistsTF{LibertinusSerif-Regular.otf}{%
      \gdef\thu@font@family@libertinus@serif{LibertinusSerif}%
      \gdef\thu@font@family@libertinus@sans{LibertinusSans}%
      \gdef\thu@font@name@libertinus@math{LibertinusMath-Regular}%
      \gdef\thu@font@style@libertinus@rm{Regular}%
      \gdef\thu@font@style@libertinus@bf{Bold}%
      \gdef\thu@font@style@libertinus@it{Italic}%
      \gdef\thu@font@style@libertinus@bfit{BoldItalic}%
    }{%
      \gdef\thu@font@family@libertinus@serif{libertinusserif}%
      \gdef\thu@font@family@libertinus@sans{libertinussans}%
      \gdef\thu@font@name@libertinus@math{libertinusmath-regular}%
      \gdef\thu@font@style@libertinus@rm{regular}%
      \gdef\thu@font@style@libertinus@bf{bold}%
      \gdef\thu@font@style@libertinus@it{italic}%
      \gdef\thu@font@style@libertinus@bfit{bolditalic}%
    }%
  \fi
}
\newcommand\thu@set@font@libertinus{%
  \thu@set@libertinus@names
  \setmainfont{\thu@font@family@libertinus@serif}[
    Extension      = .otf,
    UprightFont    = *-\thu@font@style@libertinus@rm,
    BoldFont       = *-\thu@font@style@libertinus@bf,
    ItalicFont     = *-\thu@font@style@libertinus@it,
    BoldItalicFont = *-\thu@font@style@libertinus@bfit,
  ]%
  \setsansfont{\thu@font@family@libertinus@sans}[
    Extension      = .otf,
    UprightFont    = *-\thu@font@style@libertinus@rm,
    BoldFont       = *-\thu@font@style@libertinus@bf,
    ItalicFont     = *-\thu@font@style@libertinus@it,
  ]%
  \setmonofont{lmmonolt10}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-oblique,
    BoldItalicFont = *-boldoblique,
  ]%
}
%    \end{macrocode}
%
% New Computer Modern
%    \begin{macrocode}
\newcommand\thu@set@font@newcm{%
  \setmainfont{NewCM10}[
    Extension      = .otf,
    UprightFont    = *-Book,
    BoldFont       = *-Bold,
    ItalicFont     = *-BookItalic,
    BoldItalicFont = *-BoldItalic,
  ]%
  \setsansfont{NewCMSans10}[
    Extension         = .otf,
    UprightFont       = *-Book,
    BoldFont          = *-Bold,
    ItalicFont        = *-BookOblique,
    BoldItalicFont    = *-BoldOblique,
  ]%
  \setmonofont{NewCMMono10}[
    Extension           = .otf,
    UprightFont         = *-Book,
    ItalicFont          = *-BookItalic,
    BoldFont            = *-Bold,
    BoldItalicFont      = *-BoldOblique,
  ]%
}
%    \end{macrocode}
%
% Latin Modern
%    \begin{macrocode}
\newcommand\thu@set@font@lm{%
  \setmainfont{lmroman10}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-italic,
    BoldItalicFont = *-bolditalic,
  ]%
  \setsansfont{lmsans10}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-oblique,
    BoldItalicFont = *-boldoblique,
  ]%
  \setmonofont{lmmonolt10}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-oblique,
    BoldItalicFont = *-boldoblique,
  ]%
}
%    \end{macrocode}
%
% NewTX
%    \begin{macrocode}
\newcommand\thu@set@font@newtx{%
  \RequirePackage{newtxtext}%
}
%    \end{macrocode}
%
% \subsubsection{中文字体}
%
%    \begin{macrocode}
\ifthu@cjk@font@auto
  \ifthu@fontset@mac
    \thusetup{cjk-font = mac}
  \else
    \ifthu@fontset@windows
      \IfFontExistsTF{SimSun}{
        \thusetup{cjk-font = windows}
      }{
        \IfFileExists{\thu@windows@font@dir/Simsun.ttc}{
          \thusetup{cjk-font = windows-local}
        }{
          \IfFileExists{\thu@mac@word@font@dir/Simsun.ttc}{
            \thusetup{cjk-font = mac-word}
          }{
            \thu@error{Cannot find "SimSun" font}
          }
        }
      }
    \else
      \ifthu@fontset@ubuntu
        \thusetup{cjk-font = noto}
      \else
        \thusetup{cjk-font = fandol}
      \fi
    \fi
  \fi
  \thu@debug{Detected CJK font: \thu@cjk@font}
\fi
%    \end{macrocode}
%
% Windows 的中易字体。
%    \begin{macrocode}
\newcommand\thu@set@cjk@font@windows{%
  \setCJKmainfont{SimSun}[
    AutoFakeBold = 3,
    ItalicFont   = KaiTi,
  ]%
  \setCJKsansfont{SimHei}[AutoFakeBold = 3]%
  \setCJKmonofont{FangSong}%
  \setCJKfamilyfont{zhsong}{SimSun}[AutoFakeBold = 3]%
  \setCJKfamilyfont{zhhei}{SimHei}[AutoFakeBold = 3]%
  \setCJKfamilyfont{zhkai}{KaiTi}%
  \setCJKfamilyfont{zhfs}{FangSong}%
}
%    \end{macrocode}
%
% 使用本地的 Windows 字体文件。
%
% Windows 的中易楷体和仿宋字体文件名分别为 \file{Simkai.ttf} 和
% \file{Simfang.ttf}（见
% \url{https://learn.microsoft.com/en-us/typography/fonts/windows_11_font_list}），
% 而 macOS 版 Word 对应的字体名为 \file{Kaiti.ttf} 和 \file{Fangsong.ttf}。
% 所以需要进行判断。
%    \begin{macrocode}
\@namedef{thu@set@cjk@font@windows-local}{%
  \IfFileExists{\thu@windows@font@dir/Kaiti.ttf}{
    \setCJKmainfont{SimSun}[%
      Path         = \thu@windows@font@dir/,
      Extension    = .ttc,
      AutoFakeBold = 3,
      ItalicFont   = Kaiti,
      ItalicFeatures = {Extension = .ttf},
    ]%
    \setCJKmonofont{Fangsong}[
      Path         = \thu@windows@font@dir/,
      Extension    = .ttf,
    ]%
    \setCJKfamilyfont{zhkai}{Kaiti}[
      Path         = \thu@windows@font@dir/,
      Extension    = .ttf,
    ]%
    \setCJKfamilyfont{zhfs}{Fangsong}[
      Path         = \thu@windows@font@dir/,
      Extension    = .ttf,
    ]%
  }{
    \setCJKmainfont{SimSun}[%
      Path         = \thu@windows@font@dir/,
      Extension    = .ttc,
      AutoFakeBold = 3,
      ItalicFont   = Simkai,
      ItalicFeatures = {Extension = .ttf},
    ]%
    \setCJKmonofont{Simfang}[
      Path         = \thu@windows@font@dir/,
      Extension    = .ttf,
    ]%
    \setCJKfamilyfont{zhkai}{Simkai}[
      Path         = \thu@windows@font@dir/,
      Extension    = .ttf,
    ]%
    \setCJKfamilyfont{zhfs}{Simfang}[
      Path         = \thu@windows@font@dir/,
      Extension    = .ttf,
    ]%
  }
  \setCJKsansfont{SimHei}[%
    Path         = \thu@windows@font@dir/,
    Extension    = .ttf,
    AutoFakeBold = 3,
  ]%
  \setCJKfamilyfont{zhsong}{SimSun}[%
    Path         = \thu@windows@font@dir/,
    Extension    = .ttc,
    AutoFakeBold = 3,
  ]%
  \setCJKfamilyfont{zhhei}{SimHei}[%
    Path         = \thu@windows@font@dir/,
    Extension    = .ttf,
    AutoFakeBold = 3,
  ]%
}
%    \end{macrocode}
%
% macOS 的 Microsoft Word 字体。
%    \begin{macrocode}
\@namedef{thu@set@cjk@font@mac-word}{%
  \let\thu@windows@font@dir\thu@mac@word@font@dir
  \@nameuse{thu@set@cjk@font@windows-local}%
}
%    \end{macrocode}
%
% macOS 的华文字体。
%    \begin{macrocode}
\newcommand\thu@set@cjk@font@mac{%
  \defaultCJKfontfeatures{}%
  \setCJKmainfont{Songti SC}[
    UprightFont    = * Light,
    BoldFont       = * Bold,
    ItalicFont     = Kaiti SC Regular,
    BoldItalicFont = Kaiti SC Bold,
  ]%
  \setCJKsansfont{Heiti SC}[
    UprightFont    = * Light,
    BoldFont       = * Medium,
  ]%
  \setCJKmonofont{STFangsong}
  \setCJKfamilyfont{zhsong}{Songti SC}[
    UprightFont    = * Light,
    BoldFont       = * Bold,
  ]%
  \setCJKfamilyfont{zhhei}{Heiti SC}[
    UprightFont    = * Light,
    BoldFont       = * Medium,
  ]%
  \setCJKfamilyfont{zhfs}{STFangsong}%
  \setCJKfamilyfont{zhkai}{Kaiti SC}[
    UprightFont    = * Regular,
    BoldFont       = * Bold,
  ]%
  \setCJKfamilyfont{zhli}{Baoli SC}%
  \setCJKfamilyfont{zhyuan}{Yuanyi SC}[
    UprightFont    = * Light,
    BoldFont       = * Bold,
  ]%
}
%    \end{macrocode}
%
% 思源字体。
% 注意 Noto CJK 的 regular 字重名字不带“Regular”。
%    \begin{macrocode}
\newcommand\thu@set@cjk@font@noto{%
  \defaultCJKfontfeatures{}%
  \setCJKmainfont{Noto Serif CJK SC}[
    UprightFont    = * Light,
    BoldFont       = * Bold,
    ItalicFont     = FandolKai-Regular,
    ItalicFeatures = {Extension = .otf},
    Script         = CJK,
  ]%
  \setCJKsansfont{Noto Sans CJK SC}[
    BoldFont       = * Medium,
    Script         = CJK,
  ]%
  \setCJKmonofont{Noto Sans Mono CJK SC}[
    Script         = CJK,
  ]%
  \setCJKfamilyfont{zhsong}{Noto Serif CJK SC}[
    UprightFont    = * Light,
    UprightFont    = * Bold,
    Script         = CJK,
  ]%
  \setCJKfamilyfont{zhhei}{Noto Sans CJK SC}[
    BoldFont       = * Medium,
    Script         = CJK,
  ]%
  \setCJKfamilyfont{zhfs}{FandolFang}[
    Extension      = .otf,
    UprightFont    = *-Regular,
  ]%
  \setCJKfamilyfont{zhkai}{FandolKai}[
    Extension      = .otf,
    UprightFont    = *-Regular,
  ]%
}
%    \end{macrocode}
%
% Fandol 字体。
%    \begin{macrocode}
\newcommand\thu@set@cjk@font@fandol{%
  \defaultCJKfontfeatures{}%
  \setCJKmainfont{FandolSong}[
    Extension   = .otf,
    UprightFont = *-Regular,
    BoldFont    = *-Bold,
    ItalicFont  = FandolKai-Regular,
    ItalicFeatures = {Extension = .otf},
  ]%
  \setCJKsansfont{FandolHei}[
    Extension   = .otf,
    UprightFont = *-Regular,
    BoldFont    = *-Bold,
  ]%
  \setCJKmonofont{FandolFang}[
    Extension   = .otf,
    UprightFont = *-Regular,
  ]%
  \setCJKfamilyfont{zhsong}{FandolSong}[
    Extension   = .otf,
    UprightFont = *-Regular,
    BoldFont    = *-Bold,
  ]%
  \setCJKfamilyfont{zhhei}{FandolHei}[
    Extension   = .otf,
    UprightFont = *-Regular,
    BoldFont    = *-Bold,
  ]%
  \setCJKfamilyfont{zhfs}{FandolFang}[
    Extension   = .otf,
    UprightFont = *-Regular,
  ]%
  \setCJKfamilyfont{zhkai}{FandolKai}[
    Extension   = .otf,
    UprightFont = *-Regular,
  ]%
}
\ifthu@cjk@font@none\else
  \providecommand\songti{\CJKfamily{zhsong}}
  \providecommand\heiti{\CJKfamily{zhhei}}
  \providecommand\fangsong{\CJKfamily{zhfs}}
  \providecommand\kaishu{\CJKfamily{zhkai}}
\fi
\newcommand\thu@set@cjk@font{%
  \@nameuse{thu@set@cjk@font@\thu@cjk@font}%
}
\thu@set@cjk@font
\thu@option@hook{cjk-font}{\thu@set@cjk@font}
%    \end{macrocode}
%
% \subsubsection{数学字体}
%
% 使用 \pkg{unicode-math} 配置数学符号格式。
%    \begin{macrocode}
\newcommand\thu@set@math@style{%
  \ifthu@math@style@TeX
    \thusetup{
      uppercase-greek    = upright,
      less-than-or-equal = horizontal,
      integral           = slanted,
      integral-limits    = false,
      partial            = italic,
      math-ellipsis      = AMS,
      real-part          = fraktur,
    }%
  \else
    \thusetup{
      uppercase-greek = italic,
      integral        = upright,
      partial         = upright,
      real-part       = roman,
    }%
    \ifthu@math@style@ISO
      \thusetup{
        less-than-or-equal = horizontal,
        integral-limits    = true,
        math-ellipsis      = lower,
      }%
    \else
      \ifthu@math@style@GB
        \thusetup{
          less-than-or-equal = slanted,
          integral-limits    = false,
          math-ellipsis      = centered,
        }%
      \fi
    \fi
  \fi
}
\ifthu@main@language@chinese
  \thusetup{math-style=GB}%
\else
  \thusetup{math-style=TeX}%
\fi
\thu@set@math@style
\thu@option@hook{math-style}{\thu@set@math@style}
\thu@option@hook{main-language}{%
  \ifthu@main@language@chinese
    \thusetup{math-style=GB}%
  \else
    \thusetup{math-style=TeX}%
  \fi
}
%    \end{macrocode}
%
% 针对 \pkg{unicode-math} 逐项配置数学符号。
%    \begin{macrocode}
\newcommand\thu@set@unimath@leq{%
  \ifthu@leq@horizontal
    \ifx\@begindocumenthook\@undefined
      \let\le\thu@save@leq
      \let\ge\thu@save@geq
      \let\leq\thu@save@leq
      \let\geq\thu@save@geq
    \else
      \AtBeginDocument{%
        \let\le\thu@save@leq
        \let\ge\thu@save@geq
        \let\leq\thu@save@leq
        \let\geq\thu@save@geq
      }%
    \fi
  \else
    \ifthu@leq@slanted
      \ifx\@begindocumenthook\@undefined
        \let\le\leqslant
        \let\ge\geqslant
        \let\leq\leqslant
        \let\geq\geqslant
      \else
        \AtBeginDocument{%
          \let\le\leqslant
          \let\ge\geqslant
          \let\leq\leqslant
          \let\geq\geqslant
        }%
      \fi
    \fi
  \fi
}
\newcommand\thu@set@unimath@integral@limits{%
  \ifthu@integral@limits@true
    \removenolimits{%
      \int\iint\iiint\iiiint\oint\oiint\oiiint
      \intclockwise\varointclockwise\ointctrclockwise\sumint
      \intbar\intBar\fint\cirfnint\awint\rppolint
      \scpolint\npolint\pointint\sqint\intlarhk\intx
      \intcap\intcup\upint\lowint
    }%
  \else
    \addnolimits{%
      \int\iint\iiint\iiiint\oint\oiint\oiiint
      \intclockwise\varointclockwise\ointctrclockwise\sumint
      \intbar\intBar\fint\cirfnint\awint\rppolint
      \scpolint\npolint\pointint\sqint\intlarhk\intx
      \intcap\intcup\upint\lowint
    }%
  \fi
}
\newcommand\thu@set@unimath@ellipsis{%
  \ifthu@math@ellipsis@centered
    \DeclareRobustCommand\mathellipsis{\mathinner{\unicodecdots}}%
  \else
    \DeclareRobustCommand\mathellipsis{\mathinner{\unicodeellipsis}}%
  \fi
}
\newcommand\thu@set@unimath@real@part{%
  \ifthu@real@part@roman
    \AtBeginDocument{%
      \def\Re{\operatorname{Re}}%
      \def\Im{\operatorname{Im}}%
    }%
  \else
    \AtBeginDocument{%
      \let\Re\thu@save@Re
      \let\Im\thu@save@Im
    }%
  \fi
}
\newcommand\thu@set@unimath@style{%
  \ifthu@uppercase@greek@upright
    \unimathsetup{math-style = TeX}%
  \else
    \ifthu@uppercase@greek@italic
      \unimathsetup{math-style = ISO}%
    \fi
  \fi
  \ifthu@math@style@TeX
    \unimathsetup{bold-style = TeX}%
  \else
    \unimathsetup{bold-style = ISO}%
  \fi
  \thu@set@unimath@leq
  \thu@set@unimath@integral@limits
  \ifthu@partial@upright
    \unimathsetup{partial = upright}%
  \else
    \ifthu@partial@italic
      \unimathsetup{partial = italic}%
    \fi
  \fi
  \thu@set@unimath@ellipsis
  \thu@set@unimath@real@part
}
%    \end{macrocode}
%
%    \begin{macrocode}
\newcommand\thu@qed{\rule{1ex}{1ex}}
\newcommand\thu@load@unimath{%
  \@ifpackageloaded{unicode-math}{}{%
    \RequirePackage{unicode-math}%
    \AtBeginDocument{%
      \let\thu@save@leq\leq
      \let\thu@save@geq\geq
      \let\thu@save@Re\Re
      \let\thu@save@Im\Im
    }%
%    \end{macrocode}
%
% 兼容旧的粗体命令：\pkg{bm} 的 \cs{bm} 和 \pkg{amsmath} 的 \cs{boldsymbol}。
%    \begin{macrocode}
    \DeclareRobustCommand\bm[1]{{\symbfit{##1}}}%
    \DeclareRobustCommand\boldsymbol[1]{{\symbfit{##1}}}%
%    \end{macrocode}
%
% 兼容 \pkg{amsfonts} 和 \pkg{amssymb} 中的一些命令。
%    \begin{macrocode}
    \newcommand\square{\mdlgwhtsquare}%
    \newcommand\blacksquare{\mdlgblksquare}%
    \AtBeginDocument{%
      \renewcommand\checkmark{\ensuremath{\symbol{"2713}}}%
    }%
%    \end{macrocode}
%
% 兼容 \pkg{amsthm} 的 \cs{qedsymbol}。
%    \begin{macrocode}
    \renewcommand\thu@qed{\ensuremath{\QED}}%
  }%
}
%    \end{macrocode}
%
% STIX Two Math
%    \begin{macrocode}
\newcommand\thu@set@math@font@stix{%
  \thu@set@stix@names
  \setmathfont{\thu@font@name@stix@math}[
    Extension    = .otf,
    Scale        = MatchLowercase,
    StylisticSet = \thu@xits@integral@stylistic@set,
  ]%
  \setmathfont{\thu@font@name@stix@math}[
    Extension    = .otf,
    Scale        = MatchLowercase,
    StylisticSet = 1,
    range        = {scr,bfscr},
  ]%
}
%    \end{macrocode}
%
% XITS Math
%    \begin{macrocode}
\newcommand\thu@xits@integral@stylistic@set{%
  \ifthu@integral@upright
    8%
  \fi
}
\newcommand\thu@set@math@font@xits{%
  \thu@set@xits@names
  \setmathfont{\thu@font@name@xits@math}[
    Extension    = .otf,
    StylisticSet = \thu@xits@integral@stylistic@set,
  ]%
  \setmathfont{\thu@font@name@xits@math}[
    Extension    = .otf,
    StylisticSet = 1,
    range        = {cal,bfcal},
  ]%
}
%    \end{macrocode}
%
% Libertinus Math
%    \begin{macrocode}
\newcommand\thu@libertinus@integral@stylistic@set{%
  \ifthu@integral@slanted
    8%
  \fi
}
\newcommand\thu@set@math@font@libertinus{%
  \thu@set@libertinus@names
  \setmathfont{\thu@font@name@libertinus@math}[
    Extension    = .otf,
    StylisticSet = \thu@libertinus@integral@stylistic@set,
  ]%
}
%    \end{macrocode}
%
% New Computer Modern Math
%    \begin{macrocode}
\newcommand\thu@newcm@integral@stylistic@set{%
  \ifthu@integral@upright
    2%
  \fi
}
\newcommand\thu@set@math@font@newcm{%
  \setmathfont{NewCMMath-Book}[
    Extension    = .otf,
    StylisticSet = \thu@newcm@integral@stylistic@set,
  ]%
  \setmathfont{NewCMMath-Book}[
    Extension    = .otf,
    StylisticSet = 1,
    range        = {scr,bfscr},
  ]%
  \setmathrm{NewCM10}[
    Extension      = .otf,
    UprightFont    = *-Book,
    BoldFont       = *-Bold,
    ItalicFont     = *-BookItalic,
    BoldItalicFont = *-BoldItalic,
  ]%
  \setmathsf{NewCMSans10}[
    Extension         = .otf,
    UprightFont       = *-Book,
    BoldFont          = *-Bold,
    ItalicFont        = *-BookOblique,
    BoldItalicFont    = *-BoldOblique,
  ]%
  \setmathtt{NewCMMono10}[
    Extension           = .otf,
    UprightFont         = *-Book,
    ItalicFont          = *-BookItalic,
    BoldFont            = *-Bold,
    BoldItalicFont      = *-BoldOblique,
  ]%
}
%    \end{macrocode}
%
% Latin Modern Math
%    \begin{macrocode}
\newcommand\thu@set@math@font@lm{%
  \setmathfont{latinmodern-math}[Extension=.otf]%
  \setmathrm{lmroman10}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-italic,
    BoldItalicFont = *-bolditalic,
  ]%
  \setmathsf{lmsans10}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-oblique,
    BoldItalicFont = *-boldoblique,
  ]%
  \setmathtt{lmmonolt10}[
    Extension      = .otf,
    UprightFont    = *-regular,
    BoldFont       = *-bold,
    ItalicFont     = *-oblique,
    BoldItalicFont = *-boldoblique,
  ]%
}
%    \end{macrocode}
%
% NewTX Math
%    \begin{macrocode}
\newcommand\thu@set@math@font@newtx{%
  \ifthu@font@newtx\else
    \let\thu@save@encodingdefault\encodingdefault
    \let\thu@save@rmdefault\rmdefault
    \let\thu@save@sfdefault\sfdefault
    \let\thu@save@ttdefault\ttdefault
    \RequirePackage[T1]{fontenc}%
    \renewcommand{\rmdefault}{ntxtlf}%
    \renewcommand{\sfdefault}{qhv}%
    \renewcommand{\ttdefault}{ntxtt}%
  \fi
  \ifthu@uppercase@greek@italic
    \PassOptionsToPackage{slantedGreek}{newtxmath}%
  \fi
  \ifthu@integral@upright
    \PassOptionsToPackage{upint}{newtxmath}%
  \fi
  \RequirePackage{newtxmath}
  \let\thu@save@leq\leq
  \let\thu@save@geq\geq
  \ifthu@leq@slanted
    \let\le\leqslant
    \let\ge\geqslant
    \let\leq\leqslant
    \let\geq\geqslant
  \fi
  \ifthu@integral@limits@true
    \let\ilimits@\displaylimits
  \fi
  \let\thu@save@partial\partial
  \ifthu@partial@upright
    \let\partial\uppartial
  \fi
  \ifthu@math@ellipsis@centered
    \DeclareRobustCommand\mathellipsis{\mathinner{\cdotp\cdotp\cdotp}}%
  \else
    \DeclareRobustCommand\mathellipsis{\mathinner{\ldotp\ldotp\ldotp}}%
  \fi
  \let\thu@save@Re\Re
  \let\thu@save@Im\Im
  \ifthu@real@part@roman
    \def\Re{\operatorname{Re}}%
    \def\Im{\operatorname{Im}}%
  \fi
  \RequirePackage{bm}%
  \ifthu@font@newtx\else
    \let\encodingdefault\thu@save@encodingdefault
    \let\rmdefault\thu@save@rmdefault
    \let\sfdefault\thu@save@sfdefault
    \let\ttdefault\thu@save@ttdefault
  \fi
  \DeclareRobustCommand\symup[1]{{\mathrm{##1}}}%
  \DeclareRobustCommand\symbf[1]{{\bm{##1}}}%
  \DeclareRobustCommand\symbfsf[1]{{\bm{\mathsf{##1}}}}%
  \let\increment\upDelta%
  \renewcommand\thu@qed{\openbox}%
}
%    \end{macrocode}
%
%    \begin{macrocode}
\newcommand\thu@set@math@font{%
  \ifthu@math@font@none\else
    \ifthu@math@font@newtx
      \thu@set@math@font@newtx
    \else
      \thu@load@unimath
      \thu@set@unimath@style
      \@nameuse{thu@set@math@font@\thu@math@font}%
    \fi
  \fi
}
\thu@option@hook{math-font}{\g@addto@macro\thu@setup@hook{\thu@set@math@font}}
\newcommand\thu@set@math@font@auto{%
  \ifthu@math@font@auto
    \thusetup{math-font=xits}%
  \fi
}
\AtBeginOfPackageFile*{siunitx}{\thu@set@math@font@auto}
\AtEndPreamble{\thu@set@math@font@auto}
%    \end{macrocode}
%
%
% \subsection{主文档格式}
% \label{sec:mainbody}
%
% \subsubsection{Three matters}
% \begin{macro}{\cleardoublepage}
% 对于 \textsl{openright} 选项，必须保证章首页右开，且如果前章末页无内容须
% 清空其页眉页脚。
%    \begin{macrocode}
\def\cleardoublepage{%
  \clearpage
  \if@twoside
    \ifthu@output@print
      \ifodd\c@page
      \else
        \thispagestyle{empty}%
        \hbox{}%
        \newpage
        \if@twocolumn
          \hbox{}\newpage
        \fi
      \fi
    \fi
  \fi
}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\frontmatter}
% \begin{macro}{\mainmatter}
% \begin{macro}{\backmatter}
% 我们的单面和双面模式与常规的不太一样。
%    \begin{macrocode}
\renewcommand\frontmatter{%
  \cleardoublepage
  \@mainmatterfalse
  \pagenumbering{Roman}%
}
\renewcommand\mainmatter{%
  \cleardoublepage
  \@mainmattertrue
  \pagenumbering{arabic}%
}
\renewcommand\backmatter{%
  \clearpage
  \@mainmatterfalse
  \thusetup{toc-depth = 0}%
}
%    \end{macrocode}
% \end{macro}
% \end{macro}
% \end{macro}
%
% \subsubsection{页眉页脚}
% \label{sec:headerfooter}
%
% \pkg{fancyhdr} 定义页眉页脚很方便，但是有一个非常隐蔽的坑。
% 第一次调用 \pkg{fancyhdr} 定义的样式时会修改 \cs{chaptermark}，
% 这会导致页眉信息错误（多余章号并且英文大写）。
% 这是因为在 \cs{ps@fancy} 中对 \cs{chaptermark} 进行重定义，
% 所以我们先调用 \cs{ps@fancy}，再修改 \cs{chaptermark}。
%    \begin{macrocode}
\pagestyle{fancy}
%    \end{macrocode}
%
% 定义页眉和页脚。
% 研究生要求：
% 页眉宋体五号字，宋体五号字居中书写；
% 页码五号 Times New Roman 体。
%
% 本科生要求：
% 页眉：无；
% 页码：位于页面底端，居中书写。
%
% 本科外文专业要求页码字号 12pt。
%    \begin{macrocode}
\fancypagestyle{plain}{%
  \fancyhf{}%
  \renewcommand\footrulewidth{0pt}%
  \ifthu@degree@bachelor
    \renewcommand\headrulewidth{0pt}%
    \fancyfoot[C]{
      \ifthu@main@language@chinese
        \fontsize{10.5bp}{12.075bp}\selectfont
      \else
        \normalsize
      \fi
      \thepage
    }%
    \let\@mkboth\@gobbletwo
    \let\chaptermark\@gobble
  \else
    \renewcommand\headrulewidth{0.75bp}%
    \fancyhead[C]{%
      \wuhao
      \ifthu@main@language@chinese
        \leftmark
      \else
        \MakeUppercase{\leftmark}%
      \fi
      }%
    \fancyfoot[C]{\wuhao\thepage}%
    \let\@mkboth\markboth
    \def\chaptermark##1{%
      \markboth{%
        \CTEXifname{%
          \CTEXthechapter
          \ifthu@main@language@chinese
            \quad
          \else
            \space
          \fi
        }{}##1%
      }{}%
    }%
  \fi
  \let\sectionmark\@gobble
}
\pagestyle{plain}
%    \end{macrocode}
%
% \cs{chapter} 会调用特殊的 page style。
%    \begin{macrocode}
\def\ps@chapter{}
\ctexset{chapter/pagestyle = chapter}
%    \end{macrocode}
%
%
% \subsubsection{段落}
% \label{sec:paragraph}
%
% 全文首行缩进 2 字符，标点符号用全角
%    \begin{macrocode}
\ctexset{%
  punct=quanjiao,
}
\newcommand\thu@set@indent{%
  \ifthu@main@language@chinese
    \ctexset{autoindent=2}%
  \else
    \ifthu@degree@bachelor
      \ctexset{autoindent=0.8cm}%
    \else
      \ctexset{autoindent=0.74cm}%
    \fi
  \fi
}
\thu@set@indent
\thu@option@hook{degree}{\thu@set@indent}
\thu@option@hook{main-language}{\thu@set@indent}
%    \end{macrocode}
%
% 设置 url 样式，与上下文一致
%    \begin{macrocode}
\urlstyle{same}
%    \end{macrocode}
%
% 使用 \pkg{xurl} 的方法，增加 URL 可断行的位置。
%    \begin{macrocode}
\g@addto@macro\UrlBreaks{%
  \do0\do1\do2\do3\do4\do5\do6\do7\do8\do9%
  \do\A\do\B\do\C\do\D\do\E\do\F\do\G\do\H\do\I\do\J\do\K\do\L\do\M
  \do\N\do\O\do\P\do\Q\do\R\do\S\do\T\do\U\do\V\do\W\do\X\do\Y\do\Z
  \do\a\do\b\do\c\do\d\do\e\do\f\do\g\do\h\do\i\do\j\do\k\do\l\do\m
  \do\n\do\o\do\p\do\q\do\r\do\s\do\t\do\u\do\v\do\w\do\x\do\y\do\z
}
\Urlmuskip=0mu plus 0.1mu
%    \end{macrocode}
%
% 取消列表的间距，以符合中文习惯。
%    \begin{macrocode}
\partopsep=\z@skip
\def\@listi{\leftmargin\leftmargini
            \parsep \z@skip
            \topsep \z@skip
            \itemsep\z@skip}
\let\@listI\@listi
\@listi
\def\@listii {\leftmargin\leftmarginii
              \labelwidth\leftmarginii
              \advance\labelwidth-\labelsep
              \topsep    \z@skip
              \parsep    \z@skip
              \itemsep   \z@skip}
\def\@listiii{\leftmargin\leftmarginiii
              \labelwidth\leftmarginiii
              \advance\labelwidth-\labelsep
              \topsep    \z@skip
              \parsep    \z@skip
              \partopsep \z@skip
              \itemsep   \z@skip}
%    \end{macrocode}
%
% 使用 \pkg{enumitem} 命令调整默认列表环境间的距离，
%    \begin{macrocode}
\setlist{nosep}
%    \end{macrocode}
%
%
% \subsubsection{脚注}
% \label{sec:footnote}
%
% 严格禁止脚注跨页，参考 \href{https://github.com/tuna/thuthesis/issues/778}{\#778}
% 和 \url{https://texfaq.org/FAQ-splitfoot}。
%    \begin{macrocode}
\interfootnotelinepenalty=10000
%    \end{macrocode}
%
% 脚注内容采用小五号字，中文用宋体，英文和数字用 Times New Roman 体按两端对齐格式书写，
% 单倍行距，段前段后均空 0 磅。
% 脚注的序号按页编排，不同页的脚注序号不需要连续。
%
% 脚注处序号“1，……，10”的字体是“正文”，不是“上标”，序号与脚注内容文字之间空半个汉字符，
% 脚注的段落格式为：单倍行距，段前空 0 磅，段后空 0 磅，悬挂缩进 1.5 字符；
% 字号为小五号字，汉字用宋体，外文用 Times New Roman 体。
%
% 脚注序号使用带圈数字。
% \begin{macro}{\thu@circled}
% 生成带圈的脚注数字，最多处理到 10。
%    \begin{macrocode}
\newcommand\thu@circled[1]{%
  \ifnum#1 >10\relax
    \thu@warning{%
      Too many footnotes in this page.
      Keep footnote less than 10%
    }%
  \fi
  {\symbol{\the\numexpr#1+"245F\relax}}%
}
\renewcommand{\thefootnote}{\thu@circled{\c@footnote}}
\renewcommand{\thempfootnote}{\thu@circled{\c@mpfootnote}}
%    \end{macrocode}
% \end{macro}
%
% 定义脚注分割线，字号（宋体小五），以及悬挂缩进（1.5字符）。
%    \begin{macrocode}
\def\footnoterule{\vskip-3\p@\hrule\@width0.3\textwidth\@height0.4\p@\vskip2.6\p@}
\footnotemargin=13.5bp
%    \end{macrocode}
%
% 修改 \pkg{footmisc} 定义的脚注格式。
%    \begin{macrocode}
\long\def\@makefntext#1{%
  \begingroup
    % 序号取消上标
    \def\@makefnmark{\hbox{\normalfont\@thefnmark}}%
    \xiaowu
    \ifFN@hangfoot
      \bgroup
      \setbox\@tempboxa\hbox{%
        \ifdim\footnotemargin>\z@
          \hb@xt@\footnotemargin{\@makefnmark\hss}%
        \else
          \@makefnmark
        \fi
      }%
      \leftmargin\wd\@tempboxa
      \rightmargin\z@
      \linewidth \columnwidth
      \advance \linewidth -\leftmargin
      \parshape \@ne \leftmargin \linewidth
      % \footnotesize
      \xiaowu
      \@setpar{{\@@par}}%
      \leavevmode
      \llap{\box\@tempboxa}%
      \parskip\hangfootparskip\relax
      \parindent\hangfootparindent\relax
    \else
      \parindent1em%
      \noindent
      \ifdim\footnotemargin>\z@
        \hb@xt@ \footnotemargin{\hss\@makefnmark}%
      \else
        \ifdim\footnotemargin=\z@
          \llap{\@makefnmark}%
        \else
          \llap{\hb@xt@ -\footnotemargin{\@makefnmark\hss}}%
        \fi
      \fi
    \fi
    \footnotelayout#1%
    \ifFN@hangfoot
      \par\egroup
    \fi
  \endgroup
}
%    \end{macrocode}
%
%
% \subsubsection{数学相关}
% \label{sec:equation}
% 允许太长的公式断行、分页等。
%    \begin{macrocode}
\allowdisplaybreaks[4]
%    \end{macrocode}
%
% 公式距前后文的距离由 4 个参数控制，参见 \cs{normalsize} 的定义。
%
% 中文模板的公式编号使用中文括号。需要修改 \pkg{amsmath} 的 \cs{tagform@}。
% 这里中文的 \cs{unskip} 是为了“|式~\eqref|”这样的写法不产生额外的空格。
%    \begin{macrocode}
\newcommand\thu@eqn@left@paren{（}
\newcommand\thu@eqn@right@paren{）}
\newcommand\thu@set@eqn@paren@style{%
  \ifthu@eqn@paren@style@full
    \renewcommand\thu@eqn@left@paren{（}%
    \renewcommand\thu@eqn@right@paren{）}%
  \else
    \renewcommand\thu@eqn@left@paren{(}%
    \renewcommand\thu@eqn@right@paren{)}%
  \fi
}
\thu@set@eqn@paren@style
\thu@option@hook{eqn-paren-style}{\thu@set@eqn@paren@style}
\newcommand\thu@put@parentheses[1]{%
  \ifthu@language@chinese
    \unskip
    \thu@eqn@left@paren#1\thu@eqn@right@paren
  \else
    (#1)%
  \fi
}
% \def\tagform@#1{\maketag@@@{(\ignorespaces#1\unskip\@@italiccorr)}}
\def\tagform@#1{\maketag@@@{\thu@put@parentheses{\ignorespaces#1\unskip\@@italiccorr}}}
%    \end{macrocode}
%
% 重定义 \cs{eqref}，去掉原来的的 \cs{tagform@} 中 \cs{maketag@@@} 的 \cs{hbox} 和
% \cs{m@th}，防止中文左括号与前面文字的距离过窄。
%    \begin{macrocode}
% \newcommand{\eqref}[1]{\textup{\tagform@{\ref{#1}}}}
\renewcommand{\eqref}[1]{%
  \textup{%
    \normalfont\thu@put@parentheses{%
      \ignorespaces\ref{#1}\unskip\@@italiccorr
    }%
  }%
}
%    \end{macrocode}
%
% \subsubsection{浮动对象：插图和表格}
% \label{sec:float}
%
% 图表浮动体的默认位置设为 |h|。
%    \begin{macrocode}
\def\fps@figure{htbp}
\def\fps@table{htbp}
%    \end{macrocode}
%
% 设置浮动对象和文字之间的距离
%    \begin{macrocode}
\setlength{\floatsep}{12\p@ \@plus 2\p@ \@minus 2\p@}
\setlength{\textfloatsep}{12\p@ \@plus 2\p@ \@minus 2\p@}
\setlength{\intextsep}{12\p@ \@plus 2\p@ \@minus 2\p@}
\setlength{\@fptop}{0bp \@plus1.0fil}
\setlength{\@fpsep}{12bp \@plus2.0fil}
\setlength{\@fpbot}{0bp \@plus1.0fil}
%    \end{macrocode}
%
% 由于 LaTeX2e kernel 的问题，图表等浮动体与文字前后的距离不一致，需要进行 patch。
% 参考 \href{https://github.com/tuna/thuthesis/issues/614}{tuna/thuthesis/issues\#614}、
% \url{https://www.zhihu.com/question/46618031} 和
% \url{https://tex.stackexchange.com/a/40363/82731}。
%    \begin{macrocode}
\patchcmd{\@addtocurcol}%
  {\vskip \intextsep}%
  {\edef\save@first@penalty{\the\lastpenalty}\unpenalty
   \ifnum \lastpenalty = \@M  % hopefully the OR penalty
     \unpenalty
   \else
     \penalty \save@first@penalty \relax % put it back
   \fi
   \ifnum\outputpenalty <-\@Mii
     \addvspace\intextsep
     \vskip\parskip
   \else
     \addvspace\intextsep
   \fi}%
  {}{\thu@patch@error{\@addtocurcol}}
\patchcmd{\@addtocurcol}%
  {\vskip\intextsep \ifnum\outputpenalty <-\@Mii \vskip -\parskip\fi}%
  {\ifnum\outputpenalty <-\@Mii
     \aftergroup\vskip\aftergroup\intextsep
     \aftergroup\nointerlineskip
   \else
     \vskip\intextsep
   \fi}%
  {}{\thu@patch@error{\@addtocurcol}}
\patchcmd{\@getpen}{\@M}{\@Mi}
  {}{\thu@patch@error{\@getpen}}
%    \end{macrocode}
%
% 将浮动参数设为较宽松的值。
%    \begin{macrocode}
\renewcommand{\textfraction}{0.15}
\renewcommand{\topfraction}{0.85}
\renewcommand{\bottomfraction}{0.65}
\renewcommand{\floatpagefraction}{0.60}
%    \end{macrocode}
%
% 允许用户设置图表编号的连接符。
%    \begin{macrocode}
\thu@define@key{
  figure-number-separator = {
    name    = figure@number@separator,
    default = {.},
  },
  table-number-separator = {
    name    = table@number@separator,
    default = {.},
  },
  equation-number-separator = {
    name    = equation@number@separator,
    default = {.},
  },
  number-separator = {
    name    = number@separator,
    default = {.},
  },
}
\renewcommand\thefigure{%
  \ifnum\c@chapter>\z@
    \thechapter
    \thu@figure@number@separator
  \fi
  \@arabic\c@figure
}
\renewcommand\thetable{%
  \ifnum\c@chapter>\z@
    \thechapter
    \thu@table@number@separator
  \fi
  \@arabic\c@table
}
\renewcommand\theequation{%
  \ifnum\c@chapter>\z@
    \thechapter
    \thu@equation@number@separator
  \fi
  \@arabic\c@equation
}
\newcommand\thu@set@number@separator{%
  \let\thu@figure@number@separator\thu@number@separator
  \let\thu@table@number@separator\thu@number@separator
  \let\thu@equation@number@separator\thu@number@separator
}
\thu@option@hook{number-separator}{\thu@set@number@separator}
%    \end{macrocode}
%
% 定制浮动图形和表格标题样式：
% \begin{itemize}
%   \item 图表标题字体为 11pt
%   \item 去掉图表号后面的冒号，图序与图名文字之间空一个汉字符宽度
%   \item 图：caption 在下，段前空 6 磅，段后空 12 磅
%   \item 表：caption 在上，段前空 12 磅，段后空 6 磅
% \end{itemize}
%    \begin{macrocode}
\DeclareCaptionFont{thu}{%
  \ifthu@degree@bachelor
    \fontsize{11bp}{15bp}\selectfont
  \else
    \ifthu@language@chinese
      \fontsize{11bp}{14.3bp}\selectfont
    \else
      \fontsize{11bp}{12.65bp}\selectfont
    \fi
  \fi
}
\captionsetup{
  font           = thu,
  labelsep       = quad,
  skip           = 6bp,
  figureposition = bottom,
  tableposition  = top,
}
\captionsetup[sub]{font=thu}
\renewcommand{\thesubfigure}{(\alph{subfigure})}
\renewcommand{\thesubtable}{(\alph{subtable})}
% \renewcommand{\p@subfigure}{:}
%    \end{macrocode}
%
% 研究生要求表单元格中的文字采用 11pt 宋体字，单倍行距。段前空 3 磅，段后空 3 磅。
% 对于中文，\cs{arraystretch} 需要调整为 $1 + 6 / (11 \times 1.3) \approx 1.42$。
% 对于英文，\cs{arraystretch} 需要调整为 $1 + 6 / (11 \times 1.15) \approx 1.47$。
%
% 注意不能简单地把行距设为 $\SI{11}{pt} \times 1.3 + \SI{6}{pt} = \SI{20.3}{pt}$，
% 这会导致含有多行文字的单元格中行距有误。
%
% 其他浮动体中（比如 \env{algorithm}）的字号默认同表格一致。
%    \begin{macrocode}
\newcommand\thu@set@table@font{
  \ifthu@language@chinese
    \def\thu@table@font{%
      \fontsize{11bp}{14.3bp}\selectfont
      \renewcommand\arraystretch{1.42}%
    }%
  \else
    \def\thu@table@font{%
      \fontsize{11bp}{12.65bp}\selectfont
      \renewcommand\arraystretch{1.47}%
    }%
  \fi
}
\thu@set@table@font
\thu@option@hook{language}{\thu@set@table@font}
\patchcmd\@floatboxreset{%
  \normalsize
}{%
  \thu@table@font
}{}{\thu@patch@error{\@floatboxreset}}
%    \end{macrocode}
%
% 对 \pkg{longtable} 跨页表格进行相同的设置。
%
% 在 Word 模板中按照正确的设置（需要去掉文档网格），
% 中文模板每页能装下 1 行标题、1 行表头、30 行表身，
% 英文模板每页能装下 1 行标题、1 行表头、33 行表身。
%    \begin{macrocode}
\AtEndOfPackageFile*{longtable}{
  \AtBeginEnvironment{longtable}{%
    \thu@table@font
  }
}
%    \end{macrocode}
%
% 研究生和本科生都推荐使用三线表，并且要求表的上、下边线为单直线，线粗为 1.5 磅；
% 第三条线为单直线，线粗为 1 磅。
% 这里设置 \pkg{booktabs} 线粗的默认值。
%    \begin{macrocode}
\heavyrulewidth=1.5bp
\lightrulewidth=1bp
%    \end{macrocode}
%
%    \begin{macrocode}
\AtEndOfPackageFile*{threeparttable}{
  \g@addto@macro\TPT@defaults{\wuhao}
}
%    \end{macrocode}
%
% \subsubsection{章节标题}
% \label{sec:theor}
%    \begin{macrocode}
\newcommand{\thu@abstract@name}{摘\quad 要}
\newcommand{\thu@abstract@name@en}{Abstract}
%    \end{macrocode}
%
% 各级标题格式设置。
%    \begin{macrocode}
\ctexset{%
  chapter = {
    nameformat   = {},
    numberformat = {},
    titleformat  = {},
    fixskip      = true,
    afterindent  = true,
    lofskip      = 0pt,
    lotskip      = 0pt,
  },
  section = {
    afterindent  = true,
  },
  subsection = {
    afterindent  = true,
  },
  subsubsection = {
    afterindent  = true,
  },
  paragraph/afterindent = true,
  subparagraph/afterindent = true,
}
%    \end{macrocode}
%
% 本科生要求：
% \begin{center}
%   \begin{tabular}{lclll}
%     \toprule
%     标题        & 中文       & 英文       & 段前/后间距 & 行距 \\
%     \midrule
%     章标题      & 黑体三号   & Arial 16pt & 24/18 pt    & 单倍 \\
%     一级节标题  & 黑体四号   & Arial 14pt & 24/6 pt    & 20pt \\
%     二级节标题  & 黑体 13pt & Arial 13pt & 12/6 pt    & 20pt \\
%     三级节标题  & 黑体小四号 & Arial 12pt & 12/6 pt     & 20pt \\
%     \bottomrule
%   \end{tabular}
% \end{center}
%
% 这里三级节标题的“中文黑体小四号”和“英文 Arial 13pt”不一致，取 13pt。
%    \begin{macrocode}
\newcommand\thu@set@section@format{%
  \ifthu@degree@bachelor
    \ctexset{%
      chapter = {
        format     = \centering\sffamily\fontsize{16bp}{20.8bp}\selectfont,
        titleformat = \thu@stretch{3em},
        aftername  = \quad,
        beforeskip = 27bp,
        afterskip  = 27bp,
      },
      section = {
        format     = \sffamily\fontsize{14bp}{20bp}\selectfont,
        aftername  = \quad,
        beforeskip = 24bp,
        afterskip  = 6bp,
      },
      subsection = {
        format     = \sffamily\fontsize{13bp}{20bp}\selectfont,
        aftername  = \quad,
        beforeskip = 12bp,
        afterskip  = 6bp,
      },
      subsubsection = {
        format     = \sffamily\fontsize{12bp}{20bp}\selectfont,
        aftername  = \quad,
        beforeskip = 12bp,
        afterskip  = 6bp,
      },
    }%
    \ifthu@main@language@chinese
      \ctexset{
        chapter = {
          name   = {第,章},
          number = \thechapter,
        },
      }%
    \else
      \ctexset{
        chapter = {
          name   = \chaptername\space,
          number = \thu@english@number{chapter},
        },
      }%
    \fi
%    \end{macrocode}
%
% 研究生要求：
% \begin{itemize}
%   \item 各章标题，例如：“\textsf{第 1 章 引言}”。
%
%     章序号与章名之间空一个汉字符。
%     采用黑体三号字，居中书写，单倍行距，
%     段前空 24 磅，段后空 18 磅。
%
%   \item 一级节标题，例如：“\textsf{2.1 实验装置与实验方法}”。
%
%     节标题序号与标题名之间空一个汉字符（下同）。
%     采用黑体四号（14pt）字居左书写，行距为固定值 20 磅，
%     段前空 24 磅，段后空 6 磅。
%
%   \item 二级节标题，例如：“\textsf{2.1.1 实验装置}”。
%
%     采用黑体 13pt 字居左书写，行距为固定值 20 磅，
%     段前空 12 磅，段后空 6 磅。
%
%   \item 三级节标题，例如：“\textsf{******* 归纳法}”。
%
%     采用黑体小四号（12pt）字居左书写，行距为固定值 20 磅，
%     段前空 12 磅，段后空 6 磅。
% \end{itemize}
%
% 由于 Word 的行距算法不同，这里进行了一些调整使得视觉上更接近。
%    \begin{macrocode}
  \else
    \ctexset{%
      chapter = {
        titleformat = {},
        beforeskip = 27bp,
        afterskip  = 27bp,
        number     = \thechapter,
      },
      section = {
        beforeskip = 24bp,
        afterskip  = 6bp,
      },
      subsection = {
        beforeskip = 12bp,
        afterskip  = 6bp,
      },
      subsubsection = {
        beforeskip = 12bp,
        afterskip  = 6bp,
      },
    }%
    \ifthu@main@language@chinese
      \ctexset{%
        chapter = {
          format      = \centering\sffamily\sanhao,
          nameformat  = {},
          titleformat = {},
          name        = {第,章},
          aftername   = \quad,
        },
        section = {
          format     = \sffamily\fontsize{14bp}{20bp}\selectfont,
          aftername  = \quad,
        },
        subsection = {
          format     = \sffamily\fontsize{13bp}{20bp}\selectfont,
          aftername  = \quad,
        },
        subsubsection = {
          format     = \sffamily\fontsize{12bp}{20bp}\selectfont,
          aftername  = \quad,
        },
      }%
    \else
      \ctexset{%
        chapter = {
          format      = \centering\sffamily\bfseries\fontsize{16bp}{20bp}\selectfont,
          nameformat  = \MakeUppercase,
          titleformat = \MakeUppercase,
          name        = \chaptername\space,
          aftername   = \space,
        },
        section = {
          format     = \sffamily\bfseries\fontsize{14bp}{20bp}\selectfont,
          aftername  = \space,
        },
        subsection = {
          format     = \sffamily\bfseries\fontsize{13bp}{20bp}\selectfont,
          aftername  = \space,
        },
        subsubsection = {
          format     = \sffamily\bfseries\fontsize{12bp}{20bp}\selectfont,
          aftername  = \space,
        },
      }%
    \fi
  \fi
}
\thu@set@section@format
\thu@option@hook{degree}{\thu@set@section@format}
\thu@option@hook{main-language}{\thu@set@section@format}
%    \end{macrocode}
%
%    \begin{macrocode}
\newcommand\thu@english@number[1]{%
  \expandafter\ifcase\csname c@#1\endcsname
    Zero\or
    One\or
    Two\or
    Three\or
    Four\or
    Five\or
    Six\or
    Seven\or
    Eight\or
    Nine\or
    Ten\or
    Eleven\or
    Twelve\or
    Thirteen\or
    Fourteen\or
    Fifteen\or
    Sixteen\or
    Seventeen\or
    Eighteen\or
    Nineteen\or
    Twenty\or
    \thu@error{You are genius}%
  \fi
}
%    \end{macrocode}
%
% \begin{macro}{\thu@chapter*}
% 默认的 \cs{chapter*} 很难同时满足研究生院和本科生的论文要求。本科论文要求所有的
% 章都出现在目录里，比如摘要、Abstract、主要符号表等，所以可以简单的扩展默
% 认\cs{chapter*} 实现这个目的。但是研究生又不要这些出现在目录中，而且致谢和声明
% 部分的章名、页眉和目录都不同，所以定义一个灵活的 \cs{thu@chapter*} 专门处理这些
% 要求。
%
% \cs{thu@chapter*}\oarg{tocline}\marg{title}\oarg{header}: tocline 是出现在目录
% 中的条目，如果为空则此 chapter 不出现在目录中，如果省略表示目录出现 title；
% title 是章标题；header 是页眉出现的标题，如果忽略则取 title。通过这个宏我才真
% 正体会到 \TeX{} macro 的力量！
%    \begin{macrocode}
\newcommand\thu@pdfbookmark[2]{}
\newcommand\thu@phantomsection{}
\NewDocumentCommand\thu@chapter{s o m o}{%
  \IfBooleanF{#1}{%
    \thu@error{You have to use the star form: \string\thu@chapter*}%
  }%
  \if@openright\cleardoublepage\else\clearpage\fi%
  \IfValueTF{#2}{%
    \ifthenelse{\equal{#2}{}}{%
      \thu@pdfbookmark{0}{#3}%
    }{%
      \thu@phantomsection
      \addcontentsline{toc}{chapter}{#2}%
    }%
  }{%
    \thu@phantomsection
    \addcontentsline{toc}{chapter}{#3}%
  }%
  \chapter*{#3}%
  \IfValueTF{#4}{%
    \ifthenelse{\equal{#4}{}}{%
      \@mkboth{}{}%
    }{%
      \@mkboth{#4}{#4}%
    }%
  }{%
    \@mkboth{#3}{#3}%
  }%
}
%    \end{macrocode}
% \end{macro}
%
%
% \subsubsection{目录}
% \label{sec:toc}
% 最多 4 层，即: x.x.x.x，对应的命令和层序号分别是：
% \cs{chapter}(0), \cs{section}(1), \cs{subsection}(2), \cs{subsubsection}(3)。
%    \begin{macrocode}
\setcounter{secnumdepth}{3}
\setcounter{tocdepth}{2}
%    \end{macrocode}
%
% \begin{macro}{\tableofcontents}
% 目录生成命令。
%    \begin{macrocode}
\renewcommand\tableofcontents{%
  \ifthu@degree@graduate
    \thu@chapter*{\contentsname}%
  \else
    \ifthu@degree@bachelor
      \ctexset{chapter/titleformat = \thu@stretch{2.5em}}%
      \thu@chapter*[]{\contentsname}%
      \ctexset{chapter/titleformat = \thu@stretch{3em}}%
    \else
      \thu@chapter*[]{\contentsname}%
    \fi
  \fi
  \@starttoc{toc}%
}
\thu@define@key{
  toc-chapter-style = {
    name = toc@chapter@style,
    choices = {
      arial,
      times,
    },
    default = arial,
  },
}
\thu@option@hook{toc-chapter-style}{\thu@deprecate{"toc-chapter-style" option}{}}
\newcommand\thu@contents@label@delimiter{%
  \ifthu@degree@bachelor
    \ifthu@main@language@chinese
      \space
    \else
      \quad
    \fi
  \else
    \quad
  \fi
}
\newcommand\thu@leaders{\nobreak\titlerule*[4bp]{.}\nobreak}
\newcommand\thu@set@toc@format{%
  \contentsmargin{\z@}%
%    \end{macrocode}
%
% 本科生：
% 目录从第 1 章开始，每章标题用黑体小四号字，行间距为 20pt，
% 行前空 6pt，行后空 0pt。
% 其它级节标题用宋体小四字，行间距为 20pt。
%
% 注意示例中章标题的字母和数字是衬线体，所以这里用 \cs{heiti}。
% 示例中的一级和二级节标题分别缩进 1 和 1.5 个汉字符。
%    \begin{macrocode}
  \ifthu@degree@bachelor
    \ifthu@main@language@chinese
      \titlecontents{chapter}
        [0pt]{\sffamily}
        {\contentspush{\thecontentslabel\space}\thu@stretch{3em}}{\thu@stretch{3em}}
        {\rmfamily\thu@leaders\thecontentspage}%
      \titlecontents{section}
        [1em]{}
        {\contentspush{\thecontentslabel\space}}{}
        {\thu@leaders\thecontentspage}%
      \titlecontents{subsection}
        [2em]{}
        {\contentspush{\thecontentslabel\space}}{}
        {\thu@leaders\thecontentspage}%
    \else
%    \end{macrocode}
%
% 本科生英文专业要求“左侧按级依次缩进 0.5cm”。
%    \begin{macrocode}
      \ifthu@main@language@english
        \titlecontents{chapter}
          [\z@]{\addvspace{6bp}\sffamily}
          {\contentspush{\thecontentslabel\quad}}{}
          {\rmfamily\thu@leaders\thecontentspage}%
        \titlecontents{section}
          [0.5cm]{}
          {\contentspush{\thecontentslabel\quad}}{}
          {\thu@leaders\thecontentspage}%
        \titlecontents{subsection}
          [1cm]{}
          {\contentspush{\thecontentslabel\quad}}{}
          {\thu@leaders\thecontentspage}%
      \fi
    \fi
%    \end{macrocode}
%
% 研究生：
% \begin{enumerate}
%   \item 目录中的章标题行采用黑体小四号字，固定行距 20 磅，段前段后 0 磅；
%     其他内容采用宋体小四号字，行距为固定值 20 磅，
%     段前、段后均为 0 磅。
%   \item 目录中的章标题行居左书写，一级节标题行缩进 1 个汉字符，
%     二级节标题行缩进 2 个汉字符。
% \end{enumerate}
%
% 注意示例中章标题的字母和数字是无衬线体，所以用这里用 \cs{sffamily}，
% 但是页码仍然用 \cs{rmfamily}。
%    \begin{macrocode}
  \else
    \ifthu@main@language@chinese
      \titlecontents{chapter}
        [\z@]{\sffamily}
        {\contentspush{\thecontentslabel\quad}}{}
        {\rmfamily\thu@leaders\thecontentspage}%
      \titlecontents{section}
        [1em]{}
        {\contentspush{\thecontentslabel\quad}}{}
        {\thu@leaders\thecontentspage}%
      \titlecontents{subsection}
        [2em]{}
        {\contentspush{\thecontentslabel\quad}}{}
        {\thu@leaders\thecontentspage}%
    \else
      \titlecontents{chapter}
        [\z@]{\heiti}
        {\contentspush{\MakeUppercase{\thecontentslabel}\space}\MakeUppercase}{\MakeUppercase}
        {\rmfamily\thu@leaders\thecontentspage}%
      \titlecontents{section}
        [1em]{}
        {\contentspush{\thecontentslabel\space}}{}
        {\thu@leaders\thecontentspage}%
      \titlecontents{subsection}
        [2em]{}
        {\contentspush{\thecontentslabel\space}}{}
        {\thu@leaders\thecontentspage}%
    \fi
  \fi
}
\thu@set@toc@format
\thu@option@hook{degree}{\thu@set@toc@format}
\thu@option@hook{main-language}{\thu@set@toc@format}
%    \end{macrocode}
% \end{macro}
%
%
% \subsubsection{封面和封底}
% \label{sec:cover}
% 定义密级参数。
%    \begin{macrocode}
\thu@define@key{
  secret-level = {
    name = secret@level,
  },
  secret-year = {
    name = secret@year,
  },
%    \end{macrocode}
%
% 论文中英文题目。
%    \begin{macrocode}
  title = {
    default = {标题},
  },
  title* = {
    default = {Title},
    name    = title@en,
  },
%    \end{macrocode}
%
% 作者、导师、副导师、联合指导老师。
%    \begin{macrocode}
  author = {
    default = {姓名},
  },
  author* = {
    default = {Name of author},
    name    = author@en,
  },
  student-id = {
    name = student@id,
  },
  supervisor = {
    default = {导师姓名},
  },
  supervisor* = {
    default = {Name of supervisor},
    name    = supervisor@en,
  },
  associate-supervisor = {
    name = associate@supervisor,
  },
  associate-supervisor* = {
    name = associate@supervisor@en,
  },
  co-supervisor = {
    name = co@supervisor,
  },
  co-supervisor* = {
    name = co@supervisor@en,
  },
  % Reserved for compatibility
  joint-supervisor = {
    name = co@supervisor,
  },
  joint-supervisor* = {
    name = co@supervisor@en,
  },
%    \end{macrocode}
%
% 学位中英文。
%    \begin{macrocode}
  degree-category = {
    default = {工学博士},
    name    = degree@category,
  },
  degree-category* = {
    default = {Doctor of Philosophy},
    name    = degree@category@en,
  },
}
%    \end{macrocode}
%
% 院系中英文名称。
%    \begin{macrocode}
\thu@define@key{
  department = {
    default = {计算机科学与技术系},
  },
%    \end{macrocode}
%
% 学科中英文名称。
%    \begin{macrocode}
  discipline = {
    % default = {计算机科学与技术},
  },
  discipline* = {
    % default = {Computer Science and Technology},
    name    = discipline@en,
  },
}
\thu@option@hook{discipline}{%
  \ifthu@degree@type@professional
    \thu@warning{`discipline' for professional degree is deprecated. Use `professional-field' instead.}
    \let\thu@professional@field\thu@discipline
    \let\thu@discipline\@empty
  \fi
}
\thu@option@hook{discipline*}{%
  \ifthu@degree@type@professional
    \thu@warning{`discipline*' for professional degree is deprecated. Use `professional-field*' instead.}
    \let\thu@professional@field@en\thu@discipline@en
    \let\thu@discipline@en\@empty
  \fi
}
%    \end{macrocode}
%
% 专业领域。
%    \begin{macrocode}
\thu@define@key{
  professional-field = {
    name    = professional@field,
  },
  professional-field* = {
    name    = professional@field@en,
  },
%    \end{macrocode}
%
% 工程领域。
%    \begin{macrocode}
  engineering-field = {
    name    = engineering@field,
  },
  engineering-field* = {
    name    = engineering@field@en,
  },
%    \end{macrocode}
%
% 论文成文日期。
%    \begin{macrocode}
  date = {
    default = {\the\year-\two@digits{\month}-\two@digits{\day}},
  },
%    \end{macrocode}
%
% 博士后专用封面参数。
%    \begin{macrocode}
  clc,
  udc,
  id,
  discipline-level-1 = {
    default = {一级学科名称},
    name    = discipline@level@i,
  },
  discipline-level-2 = {
    default = {二级学科名称},
    name    = discipline@level@ii,
  },
  start-date = {
    name    = start@date,
    default = {\the\year-\two@digits{\month}-\two@digits{\day}},
  },
  end-date = {
    name    = end@date,
    default = {\the\year-\two@digits{\month}-\two@digits{\day}},
  },
%    \end{macrocode}
%
% 中文封面后是否生成书脊页。
%    \begin{macrocode}
  include-spine = {
    name = include@spine,
    choices = {
      false,
      true,
    },
    default = false,
  },
}
%    \end{macrocode}
%
% 输出日期的给定格式：\cs{thu@format@date}\marg{format}\marg{date}，
% 其中格式 \meta{format} 接受三个参数分别对应年、月、日，
% \meta{date} 是 ISO 格式的日期（yyyy-mm-dd）。
%    \begin{macrocode}
\newcommand\thu@format@date[2]{%
  \edef\thu@@date{#2}%
  \def\thu@@process@date##1-##2-##3\@nil{%
    #1{##1}{##2}{##3}%
  }%
  \expandafter\thu@@process@date\thu@@date\@nil
}
\newcommand\thu@date@zh@digit[3]{#1 年 \number#2 月 \number#3 日}
\newcommand\thu@date@zh@digit@short[3]{#1 年 \number#2 月}
\newcommand\thu@date@zh@short[3]{\zhdigits{#1}年\zhnumber{#2}月}
\newcommand\thu@date@month[1]{%
  \ifcase\number#1\or
    January\or February\or March\or April\or May\or June\or
    July\or August\or September\or October\or November\or December%
  \fi
}
\newcommand\thu@date@en@short[3]{\thu@date@month{#2}, #1}
%    \end{macrocode}
%
% 下划线命令。
% \pkg{ulem} 的下划线 \cs{uline} 可以控制粗细和深度。
%    \begin{macrocode}
\newcommand\thu@underline[2][6em]{\hskip1pt\underline{\hb@xt@ #1{\hss#2\hss}}\hskip3pt}
\newcommand\thu@uline[2][6em]{\uline{\hb@xt@ #1{\hss#2\hss}}}
%    \end{macrocode}
%
% 将内容拉伸或压缩到固定宽度。
%    \begin{macrocode}
\newcommand\thu@fixed@box[2]{%
  \begingroup
    \ifLuaTeX
      \ltjsetparameter{kanjiskip = {0pt plus 2filll minus 1filll}}%
    \else
      \renewcommand\CJKglue{\hspace{0pt plus 2filll minus 1filll}}%
    \fi
    \makebox[#1][l]{#2}%
  \endgroup
}
%    \end{macrocode}
%
% 如果内容小于给定宽度，则拉伸至该宽度，否则取自然宽度。
%    \begin{macrocode}
\newbox\thu@stretch@box
\newcommand\thu@stretch[2]{%
  \sbox\thu@stretch@box{#2}%
  \ifdim \wd\thu@stretch@box < #1\relax
    \begingroup
      \ifLuaTeX
        \ltjsetparameter{kanjiskip = {0pt plus 2filll}}%
      \else
        \renewcommand\CJKglue{\hspace{0pt plus 2filll}}%
      \fi
      \makebox[#1][l]{#2}%
    \endgroup
  \else
    \box\thu@stretch@box
  \fi
}
%    \end{macrocode}
%
% 如果内容小于给定宽度，则在右侧填充空白至该宽度，否则取自然宽度。
%    \begin{macrocode}
\newbox\thu@pad@box
\newcommand\thu@pad[2]{%
  \sbox\thu@pad@box{#2}%
  \ifdim \wd\thu@pad@box < #1\relax
    \makebox[#1][l]{\box\thu@pad@box}%
  \else
    \box\thu@pad@box
  \fi
}
%    \end{macrocode}
%
% 导师的姓名和职称使用“,”分开，所以这里用 \pkg{kvsetkeys} 的 \cs{comma@parse} 来处理。
%    \begin{macrocode}
\newcounter{thu@csl@count}
\newcommand\thu@name@title@process[1]{%
  \ifcase\c@thu@csl@count  % == 0
    \gdef\thu@@name{#1}%
  \or  % == 1
    \gdef\thu@@title{#1}%
  \fi
  \stepcounter{thu@csl@count}%
}
\newcommand\thu@name@title@format[2]{%
  \thu@pad{3cm}{\thu@stretch{4em}{#1}}%
  \thu@stretch{3em}{#2}%
}
\newcommand\thu@name@title[1]{%
  \setcounter{thu@csl@count}{0}%
  \gdef\thu@@name{}%
  \gdef\thu@@title{}%
  \expandafter\comma@parse\expandafter{#1}{\thu@name@title@process}%
  \thu@name@title@format{\thu@@name}{\thu@@title}%
}
%    \end{macrocode}
%
% \myentry{封面}
% \begin{macro}{\maketitle}
% 生成封面（题名页）总命令。
%    \begin{macrocode}
\renewcommand\maketitle{%
  \cleardoublepage
  \pagenumbering{Alph}%
  \thu@pdfbookmark{-1}{\thu@title}%
  \thu@titlepage
  \ifthu@include@spine@true
    \spine
  \fi
  \ifthu@degree@graduate
    \ifthu@thesis@type@thesis
      \cleardoublepage
      \thu@titlepage@en
    \fi
  \fi
  \clearpage
}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\thu@titlepage}
% 中文封面（题名页）
%
%    \begin{macrocode}
\newcommand\thu@titlepage{%
  \thusetup{language = chinese}%
  \ifthu@degree@graduate
    % 研究生
    \thu@titlepage@thesis
  \else
    \ifthu@degree@bachelor
      % 本科生
      \thu@titlepage@bachelor
    \else
      \ifthu@degree@postdoc
        % 博后
        \thu@cover@postdoc
        \cleardoublepage
        \thu@titlepage@postdoc
      \fi
    \fi
  \fi
  \thu@reset@main@language
}
%    \end{macrocode}
% \end{macro}
%
% \myentry{研究生中文封面}
% 《写作指南》规定中文封面页边距：
% 上—6. 0 厘米，下—5.5 厘米，左—4.0 厘米，右—4.0 厘米，装订线 0 厘米。
% 然而作为事实标准的 Word 模板的页边距是上下 6.0 厘米，左右 4.0 厘米。
% 这里缩小上边距以方便排版保密信息。
%    \begin{macrocode}
\newcommand\thu@titlepage@thesis{%
  \newgeometry{
    top     = 2cm,
    bottom  = 6cm,
    hmargin = 3.5cm,
  }%
  \thispagestyle{empty}%
  \null\vskip 8.1pt%
  \begingroup
    \centering
    \parbox[t][2cm][t]{\textwidth}{%
      \hskip -21.5pt%
      \thu@titlepage@secret
    }\par
    \vskip 40.5pt%
    \begingroup
      \sffamily\fontsize{26bp}{46.8bp}\selectfont
      \thu@title\par
    \endgroup
    \ifthu@main@language@english
      \vskip 5.4pt%
      \begingroup
        \sffamily\bfseries\fontsize{20bp}{31.2bp}\selectfont
        \thu@title@en\par
      \endgroup
      \vskip -9.2pt%
    \fi
    \vskip 24.1pt%
    \thu@title@page@degree@category\par
    \vfill
    \ifthu@degree@type@academic
      \parbox[t][7.25cm][t]{\textwidth}{%
        \fangsong\fontsize{16bp}{31.2bp}\selectfont
        \thu@titlepage@info
      }\par
    \else
      \parbox[t][5.25cm][b]{\textwidth}{%
        \fangsong\fontsize{16bp}{31.2bp}\selectfont
        \thu@titlepage@info
      }\par
      \vskip 62pt%
    \fi
    \parbox[t][1.03cm][t]{\textwidth}{\centering\thu@titlepage@date}\par
  \endgroup
  \clearpage
  \restoregeometry
}
%    \end{macrocode}
%
% 选题报告封面需要提供学号
%    \begin{macrocode}
\newcommand\thu@set@student@id{%
  \ifthu@thesis@type@proposal\else
    \ifx\thu@student@id\@empty\else
      \thu@warning{`student-id' in "\protect\thusetup" would be ignored when `thesis-type' is not proposal.}%
    \fi
  \fi
}
\thu@set@student@id
\thu@option@hook{thesis-type}{\thu@set@student@id}
\thu@option@hook{student-id}{\thu@set@student@id}
%    \end{macrocode}
%
% 涉密信息
%    \begin{macrocode}
\newcommand\thu@titlepage@secret{%
  \sffamily\sanhao
  \ifx\thu@secret@level\@empty
    \phantom{秘密}%
  \else
    \thu@secret@level\symbol{"2605}\makebox[3em][c]{\thu@secret@year}年%
  \fi\par
}
%    \end{macrocode}
%
% 申请学位的学科门类或专业学位类别: 三号（16bp）宋体字，字距延伸 0.5bp，
% 所以 \cs{CJKglue} 应该设为 1 bp。
%    \begin{macrocode}
\newcommand\thu@title@page@degree@category{%
  \begingroup
    \fontsize{16bp}{22bp}\selectfont
    \ifLuaTeX
      \fontspec{\CJK@family}%
      \ltjsetparameter{kanjiskip = {1bp}}%
    \else
      \CJKfamily+{}%
      \renewcommand\CJKglue{\hspace{1bp}}%
    \fi
    \ifthu@thesis@type@thesis
      (申请清华大学\thu@degree@category
      \ifthu@degree@type@professional
        专业%
      \fi
      学位论文)%
    \else
      \ifthu@thesis@type@proposal
        (清华大学%
        \ifthu@degree@doctor
          博士%
        \else
          \ifthu@degree@master
            硕士%
          \fi
        \fi
        学位论文选题报告)%
      \fi
    \fi
    \par
  \endgroup
}
%    \end{macrocode}
%
% 作者及指导教师信息
%    \begin{macrocode}
\newcommand\thu@titlepage@info{%
  \thu@titlepage@info@tabular{2.3cm}{2.85cm}{2.75cm}{0.77cm}{%
    \thu@info@item{培养单位}{}{\thu@department}%
    \ifthu@degree@type@academic
      \thu@info@item{学科}{}{\thu@discipline}%
      \thu@info@item{研究生}{\thu@name@title}{\thu@author}%
    \else
      \thu@info@item{专业领域}{}{\thu@professional@field}%
      \thu@info@item{工程领域}{}{\thu@engineering@field}%
      \thu@info@item{申请人}{\thu@name@title}{\thu@author}%
    \fi
    \ifthu@thesis@type@proposal
      \ifx\thu@student@id\@empty
        \thu@warning{Missing option `student-id' in "\protect\thusetup", ID will not appear on cover.}%
      \else
        \thu@info@item{学号}{}{\thu@student@id}%
      \fi
    \fi
    \thu@info@item{指导教师}{\thu@name@title}{\thu@supervisor}%
    \thu@info@item{副指导教师}{\thu@name@title}{\thu@associate@supervisor}%
    \thu@info@item{联合指导教师}{\thu@name@title}{\thu@co@supervisor}%
  }\par
}
%    \end{macrocode}
%
% 标题页作者信息表
% \texttt{\#1}: 表格左侧至版心的距离；\\
% \texttt{\#2}: “培养单位”的边框宽度；\\
% \texttt{\#3}: “培养单位”的文字宽度；\\
% \texttt{\#4}: 冒号的边框；\\
% \texttt{\#5}: 表格内容。
%    \begin{macrocode}
\newcommand\thu@titlepage@info@tabular[5]{%
  \def\thu@info@item##1##2##3{%
    \ifx##3\@empty\else
      \thu@pad{#2}{\thu@fixed@box{#3}{##1}}%
      \thu@pad{#4}{：}%
      ##2{##3}\\
    \fi
  }%
  \hspace{#1}%
  \begin{tabular}{l}%
    \renewcommand\arraystretch{1}%
    #5%
  \end{tabular}%
}
%    \end{macrocode}
%
% 论文成文打印的日期，用三号宋体汉字，字距延伸 0.5bp，
% 所以 \cs{CJKglue} 应该设为 1 bp。
%    \begin{macrocode}
\newcommand\thu@titlepage@date{%
  \begingroup
    \sanhao
    \ifLuaTeX
      \ltjsetparameter{kanjiskip = {1bp}}%
    \else
      \renewcommand\CJKglue{\hspace{1bp}}%
    \fi
    \thu@format@date{\thu@date@zh@short}{\thu@date}\par
  \endgroup
}
%    \end{macrocode}
%
% \myentry{研究生英文封面}
% \begin{macro}{\thu@titlepage@en}
%    \begin{macrocode}
\newcommand{\thu@titlepage@en}{%
  \newgeometry{
    top     = 5.5cm,
    bottom  = 5cm,
    hmargin = 3.4cm,
  }%
  \thispagestyle{empty}%
  \thusetup{language = english}%
  \ifthu@degree@type@academic
    \thu@titlepage@en@graduate@academic
  \else
    \thu@titlepage@en@graduate@professional
  \fi
  \thu@reset@main@language
  \clearpage
  \restoregeometry
}
\newcommand\thu@titlepage@en@graduate@academic{%
  \begingroup
    \centering
    \null\vskip -0.31cm%
    \parbox[t][143bp][t]{\textwidth}{%
      \centering\thu@titlepage@en@title
    }\par
    \sanhao[1.725]%
    \thu@titlepage@en@degree
    \vskip 3bp%
    in\par
    \vskip 3.5bp%
    {\bfseries\sffamily\thu@discipline@en\par}
    \vfill
    {\sffamily by\par}
    \vskip 0.24cm%
    {\sffamily\bfseries\thu@author@en\par}%
    \vskip 0.18cm%
    \parbox[t][3.0cm][t]{\textwidth}{%
      \centering
      \xiaosan[2.1]%
      \thu@titlepage@en@supervisor
    }\par
    \thu@titlepage@en@date
    \vskip 0.7cm%
  \endgroup
}
\newcommand\thu@titlepage@en@graduate@professional{%
  \begingroup
    \centering
    \null\vskip -0.31cm%
    \parbox[t][143bp][t]{\textwidth}{%
      \centering\thu@titlepage@en@title
    }\par
    \sanhao[1.725]%
    \thu@titlepage@en@degree
    \vfill
    {\sffamily by\par}
    \vskip 0.24cm%
    {\sffamily\bfseries\thu@author@en\par}%
    \ifx\thu@professional@field@en\empty
      \vskip 1.95cm%
    \else
      \vskip -0.1cm%
      {\sffamily\bfseries(\thu@professional@field@en)\par}%
      \vskip 1.1cm%
    \fi
    \parbox[t][3.37cm][t]{\textwidth}{%
      \centering
      \xiaosan[1.82]%
      \thu@titlepage@en@supervisor
    }\par
    \thu@titlepage@en@date
    \vskip 0.3cm%
  \endgroup
}
\newcommand\thu@titlepage@en@title{%
  \begingroup
    % 对齐到网格，每行 15.6bp
    \sffamily\bfseries\fontsize{20bp}{31.2bp}\selectfont
    \thu@title@en\par
  \endgroup
}
\newcommand\thu@thesis@name@en{%
  \ifthu@degree@master
    Thesis%
  \else
    Dissertation%
  \fi
}
\newcommand\thu@titlepage@en@degree{%
  \thu@thesis@name@en{} submitted to\par
  {\bfseries Tsinghua University\par}%
  in partial fulfillment of the requirement\par
  for the
  \ifthu@degree@type@professional
    professional
  \fi
  degree of\par
  {\sffamily\bfseries\thu@degree@category@en\par}%
}
\newcommand\thu@titlepage@en@supervisor{%
  \begin{tabular}{r@{\makebox[20.5bp][l]{\hspace{2bp}:}}l}%
    \renewcommand\arraystretch{1}%
    \thu@thesis@name@en{} Supervisor & \thu@supervisor@en \\
    \ifx\thu@associate@supervisor@en\@empty\else
      Associate Supervisor           & \thu@associate@supervisor@en \\
    \fi
    \ifx\thu@co@supervisor@en\@empty\else
      Co-supervisor                  & \thu@co@supervisor@en \\
    \fi
  \end{tabular}%
}
\newcommand\thu@titlepage@en@date{%
  \begingroup
    \sffamily\bfseries\sanhao
    \thu@format@date{\thu@date@en@short}{\thu@date}\par
  \endgroup
}
%    \end{macrocode}
% \end{macro}
%
% \myentry{本科生封面}
% 本科生封面要求：
% \begin{itemize}
%   \item 题目：1 号黑体字，1.2 倍行距。
%   \item 系别、专业、姓名及指导教师信息部分使用三号仿宋\_GB2312 字。
%   \item 论文成文打印的日期用阿拉伯数字，采用小四号宋体。
%   \item 涉密的论文在封面右上角处注明论文密级，采用小四号宋体。
% \end{itemize}
%
% 外文系英语专业要求题目先写中文标题，再写英文标题，字号 26pt，32 磅行距。
%    \begin{macrocode}
\newcommand\thu@titlepage@bachelor{%
  \newgeometry{
    top    = 3.8cm,
    bottom = 3.2cm,
    left   = 3.2cm,  % 装订线靠左 0.2 cm
    right  = 3cm,
  }%
  \thispagestyle{empty}%
  \begingroup
    \centering
    \parbox[t][0cm][t]{\textwidth}{%
      \hfill
      \xiaosi
      \ifx\thu@secret@level\@empty\else
        \thu@secret@level\space\thu@secret@year 年\par
      \fi
    }\par
  \endgroup
  \vspace{19bp}%
  \begingroup
    \centering
    \hspace*{-5bp}%
    \includegraphics[width=50.4bp]{thu-fig-logo.pdf}%
    \hspace{10bp}%
    \raisebox{7bp}{\includegraphics[width=117bp]{thu-text-logo.pdf}}%
    \par
    \vspace{17bp}%
    \begingroup
      \sffamily\bfseries\xiaochu\ziju{0.3}%
      综合论文训练%
      \ifthu@thesis@type@proposal
        \\开题报告
      \fi
      \par
    \endgroup
    \vspace{48bp}%
    \parbox[t][136bp]{\linewidth}{%
      \centering
      \heiti\fontsize{26bp}{32.5bp}\selectfont
      \thu@title\par
      \ifthu@main@language@english
        \thusetup{language=english}%
        \thu@title@en\par
        \thusetup{language=chinese}%
      \fi
    }\par
  \endgroup
  \begingroup
    \fangsong
    \fontsize{16bp}{30.96bp}\selectfont
    \noindent
    \def\thu@name@title@format##1##2{%
      \thu@stretch{4em}{##1}%
      \hspace{1.5em}%
      \thu@stretch{2.5em}{##2}%
    }%
    \thu@titlepage@info@tabular{81bp}{2.5cm}{4em}{0.82cm}{%
      \thu@info@item{系别}{}{\thu@department}%
      \thu@info@item{专业}{}{\thu@discipline}%
      \thu@info@item{姓名}{\thu@name@title}{\thu@author}%
      \thu@info@item{指导教师}{\thu@name@title}{\thu@supervisor}%
      \thu@info@item{副指导教师}{\thu@name@title}{\thu@associate@supervisor}%
      \thu@info@item{联合指导教师}{\thu@name@title}{\thu@co@supervisor}%
    }\par
  \endgroup
  \vfill
  \begingroup
    \centering
    \fontsize{16bp}{24bp}\selectfont
    \ziju{0.03}%
    \thu@format@date{\thu@date@zh@short}{\thu@date}\par
  \endgroup
  \vspace*{60bp}%
  \clearpage
  \restoregeometry
}
%    \end{macrocode}
%
% \myentry{博士后封面}
%    \begin{macrocode}
\newcommand\thu@cover@postdoc{%
  \thispagestyle{empty}%
  \begin{center}%
    \renewcommand\ULthickness{0.7pt}%
    \vspace*{0.35cm}%
    {\sihao[2.6]%
      \thu@stretch{3.1em}{分类号}\thu@underline[3.7cm]{\thu@clc}\hfill
      密级\thu@underline[3.7cm]{\thu@secret@level}\par
      \thu@stretch{3.1em}{U D C}\thu@underline[3.7cm]{\thu@udc}\hfill
      编号\thu@underline[3.7cm]{\thu@id}\par
    }%
    \vskip 3.15cm%
    {\sffamily\bfseries\xiaoer[2.6]%
      {\ziju{1.5}清华大学\par}%
      {\ziju{0.5}博士后研究工作报告\par}%
    }%
    \vskip 0.2cm%
    \parbox[t][4.0cm][c]{\textwidth}{%
      \centering\sihao[3.46]%
      \renewcommand\ULdepth{1em}%
      \expandafter\uline\expandafter{\thu@title}\par
    }\par
    \vskip 0.4cm%
    {\xiaosi\thu@author\par}%
    \vskip 1.4cm%
    {\xiaosi[1.58]%
      \renewcommand\ULdepth{0.9em}%
      工作完成日期\quad
      \thu@uline[5.9cm]{%
        \thu@format@date{\thu@date@zh@digit@short}{\thu@start@date}—%
        \thu@format@date{\thu@date@zh@digit@short}{\thu@end@date}
      }\par
      \vskip 0.55cm%
      报告提交日期\quad
      \thu@uline[5.9cm]{\thu@format@date{\thu@date@zh@digit@short}{\thu@date}}\par
    }%
    \vskip 0.45cm%
    {\xiaosi[2]{\ziju{1}清华大学}\quad （北京）\par}%
    \vskip 0.25cm%
    {\xiaosi[2]\thu@format@date{\thu@date@zh@digit@short}{\thu@date}\par}%
  \end{center}%
}
%    \end{macrocode}
%
% \myentry{博士后题名页}
%    \begin{macrocode}
\newcommand\thu@titlepage@postdoc{%
  \thispagestyle{empty}%
  \begin{center}%
    \vspace*{1.5cm}%
    \parbox[t][3cm][c]{\textwidth}{%
      \centering\sanhao[1.95]\thu@title\par
    }\par
    \vskip 0.15cm%
    \parbox[t][3cm][c]{\textwidth}{%
      \centering\sihao[1.36]\thu@title@en\par
    }\par
    \vskip 0.4cm%
    {\xiaosi[2.6]%
      \begin{tabular}{l@{\quad}l}%
        \renewcommand\arraystretch{1}%
        \thu@stretch{11em}{博士后姓名}                  & \thu<AUTHOR>
        \thu@stretch{11em}{流动站（一级学科）名称}      & \thu@discipline@level@i  \\
        \thu@stretch{11em}{专\quad{}业（二级学科）名称} & \thu@discipline@level@ii \\
      \end{tabular}\par
    }%
    \vskip 2.7cm%
    {\xiaosi[2.6]%
      研究工作起始时间\quad\thu@format@date{\thu@date@zh@digit}{\thu@start@date}\par
      \vskip 0.1cm%
      研究工作期满时间\quad\thu@format@date{\thu@date@zh@digit}{\thu@end@date}\par
    }%
    \vskip 2.1cm%
    {\xiaosi[2.6]清华大学人事处（北京）\par}%
    \vskip 0.6cm%
    {\wuhao\thu@format@date{\thu@date@zh@digit@short}{\thu@date}\par}%
  \end{center}%
}
%    \end{macrocode}
%
% \subsubsection{答辩委员会名单}
% \begin{environment}{committee}
% 学位论文指导小组、公开评阅人和答辩委员会名单。
%    \begin{macrocode}
\def\thu@committee@name{学位论文指导小组、公开评阅人和答辩委员会名单}
\NewEnviron{committee}[1][]{%
  \ifthu@degree@graduate
    \cleardoublepage
    \let\thu@committee@file\@empty
    \kv@define@key{thu@committee}{name}{\let\thu@committee@name\kv@value}%
    \kv@define@key{thu@committee}{file}{\let\thu@committee@file\kv@value}%
    \kv@set@family@handler{thu@committee}{%
      \ifx\kv@value\relax
        \let\thu@committee@file\kv@key
      \else
        \kv@handled@false
      \fi
    }%
    \kvsetkeys{thu@committee}{#1}%
    \ifx\thu@committee@file\@empty
      \begingroup
        \ctexset{
          chapter = {
            format    = \centering\sffamily\fontsize{16bp}{20bp}\selectfont,
            afterskip = 49bp,
          },
          section = {
            beforeskip  =  26bp,
            afterskip   =  9.5bp,
            format      += \centering,
            numbering   =  false,
            afterindent =  false,
          },
        }%
        \thu@chapter*[]{\thu@committee@name}%
        \thispagestyle{empty}%
        \thusetup{language=chinese}%
        \BODY\clearpage
        \thu@reset@main@language
      \endgroup
    \else
      \thu@pdfbookmark{0}{\thu@committee@name}%
      \includepdf{\thu@committee@file}%
    \fi
  \fi
}
%    \end{macrocode}
% \end{environment}
%
% \subsubsection{授权说明}
% \begin{macro}{\copyrightpage}
% 授权说明
%    \begin{macrocode}
\newcommand\copyrightpage[1][]{%
  \ifthu@degree@postdoc\relax\else
    \cleardoublepage
    \def\thu@@tmp{#1}
    \ifx\thu@@tmp\@empty
      \thusetup{language=chinese}%
      \ifthu@degree@bachelor
        \thu@copyright@page@bachelor
      \else
        \thu@copyright@page@graduate
      \fi
      \clearpage
      \thu@reset@main@language
    \else
      \thispagestyle{empty}%
      \thu@pdfbookmark{0}{关于学位论文使用授权的说明}%
      \thu@phantomsection
      \kv@define@key{thu@copyright}{file}{\includepdf{\kv@value}}%
      \kv@set@family@handler{thu@copyright}{%
        \ifx\kv@value\relax
          \includepdf{\kv@key}%
        \else
          \kv@handled@false
        \fi
      }%
      \kvsetkeys{thu@copyright}{#1}%
    \fi
  \fi
}
%    \end{macrocode}
%
% 支持扫描文件替换。
%    \begin{macrocode}
\newcommand{\thu@authorization@frontdate}{%
  日\ifthu@degree@bachelor\hspace{1em}\else\hspace{2em}\fi 期：}
\newcommand\thu@copyright@page@graduate{%
  \begingroup
    \ctexset{
      chapter = {
        format     = {\centering\sffamily\erhao},
        beforeskip = 40bp,
        afterskip  = 36bp,
      },
    }%
    \thu@chapter*[]{关于学位论文使用授权的说明}%
  \endgroup
  \thispagestyle{empty}%
  \vskip 13bp%
  \begingroup
    \fontsize{14bp}{26bp}\selectfont
    本人完全了解清华大学有关保留、使用学位论文的规定，即：\par
    清华大学拥有在著作权法规定范围内学位论文的使用权，其中包括：%
    （1）\nobreak 已获学位的研究生必须按学校规定提交学位论文，%
    学校可以采用影印、缩印或其他复制手段保存研究生上交的学位论文；\allowbreak
    （2）\nobreak 为教学和科研目的，学校可以将公开的学位论文作为资料在图书馆、资料室等场所供校内师生阅读，%
    或在校园网上供校内师生浏览部分内容；\allowbreak
    \ifthu@degree@doctor
      （3）\nobreak 根据《中华人民共和国学位法》及上级教育主管部门具体要求，向国家图书馆报送相应的学位论文。%
    \else
      （3）\nobreak 按照上级教育主管部门督导、抽查等要求，报送相应的学位论文。%
    \fi
    \par
    本人保证遵守上述规定。\par
  \endgroup
  \vskip 33bp%
  \begingroup
    \fontsize{12bp}{23.4bp}\selectfont
    \parindent\z@
    \leftskip 43bp%
    作者签名：\hspace{4bp}\thu@underline[7em]{}\hspace{47bp}%
    导师签名：\hspace{4bp}\thu@underline[7em]{}\par
    \vskip 6bp%
    日\hspace{2em}期：\hspace{4bp}\thu@underline[7em]{}\hspace{47bp}%
    日\hspace{2em}期：\hspace{4bp}\thu@underline[7em]{}\par
  \endgroup
}
\newcommand\thu@copyright@page@bachelor{%
  \begingroup
    \ctexset{
      chapter = {
        format     = {\centering\sffamily\erhao},
        beforeskip = 40bp,
        afterskip  = 37bp,
      },
    }%
    \thu@chapter*[]{关于论文使用授权的说明}%
  \endgroup
  \thispagestyle{empty}%
  \vspace*{13bp}%
  \begingroup
    \fontsize{14bp}{26bp}\selectfont
    本人完全了解清华大学有关保留、使用综合论文训练论文的规定，即：%
    学校有权保留论文的复印件，允许论文被查阅和借阅；%
    学校可以公布论文的全部或部分内容，可以采用影印、缩印或其他复制手段保存论文。\par
  \endgroup
  \vspace{71bp}%
  \begingroup
    \setlength{\parindent}{0pt}%
    \fontsize{12bp}{18bp}\selectfont
    \hspace*{42bp}作者签名：\hspace{118bp}导师签名：\par
    \vspace{11bp}%
    \hspace*{42bp}日\hspace{2em}期：\hspace{118bp}日\hspace{2em}期：\par
  \endgroup
}
%    \end{macrocode}
% \end{macro}
%
% \subsubsection{摘要}
% \label{sec:abstractformat}
%
% \begin{macro}{\thu@clist@use}
% 不同论文格式关键词之间的分割不太相同，我们用 \option{keywords} 和
% \option{keywords*} 来收集关键词列表，然后用本命令来生成符合要求的格式，
% 类似于 \LaTeX3 的 \cs{clist\_use:Nn}。
%    \begin{macrocode}
\thu@define@key{
  keywords,
  keywords* = {
    name = keywords@en,
  },
}
\newcommand\thu@clist@use[2]{%
  \def\thu@@tmp{}%
  \def\thu@clist@processor##1{%
    \ifx\thu@@tmp\@empty
      \def\thu@@tmp{#2}%
    \else
      #2%
    \fi
    ##1%
  }%
  \expandafter\comma@parse\expandafter{#1}{\thu@clist@processor}%
}
%    \end{macrocode}
% \end{macro}
%
% \begin{environment}{abstract}
% 中文摘要部分的标题为“\textbf{摘要}”，用黑体三号字。
% 摘要内容用小四号字书写，两端对齐，汉字用宋体，外文字用 Times New Roman 体，
% 标点符号一律用中文输入状态下的标点符号。
%    \begin{macrocode}
\newenvironment{abstract}{%
  \thusetup{language = chinese}%
  \ifthu@degree@graduate
    \begingroup
      \ifthu@main@language@english
        \ctexset{%
          chapter/format = \centering\sffamily\fontsize{16bp}{20bp}\selectfont,
        }%
      \fi
      \thu@chapter*{\thu@abstract@name}%
    \endgroup
  \else
    \thu@chapter*[]{\thu@abstract@name}%
  \fi
}{%
  \par
  \null\par
  \ifthu@degree@postdoc
    \textbf{关键词：}%
  \else
    \noindent
    \textsf{关键词：}%
  \fi
  \thu@clist@use{\thu@keywords}{；}\par
  \gdef\thu@keywords{}%
  \thu@reset@main@language % switch back to main language
}
%    \end{macrocode}
% \end{environment}
%
% \begin{environment}{abstract*}
% 英文摘要部分的标题为 \textbf{Abstract}，用 Arial 体三号字。
% 摘要内容用小四号 Times New Roman。
%    \begin{macrocode}
\newenvironment{abstract*}{%
  \thusetup{language = english}%
  \ifthu@degree@bachelor
    \begingroup
      \ctexset{chapter/afterskip = 30bp}%
      \thu@chapter*[]{\thu@abstract@name@en}%
    \endgroup
  \else
    \ifthu@degree@graduate
      \thu@chapter*{\thu@abstract@name@en}%
    \else
      \thu@chapter*[]{\thu@abstract@name@en}%
    \fi
  \fi
}{%
  \par
  \null\par
  \ifthu@degree@postdoc\else
    \noindent
  \fi
  \textbf{Keywords:} \thu@clist@use{\thu@keywords@en}{; }\par
  \thu@reset@main@language % switch back to main language
}
%    \end{macrocode}
% \end{environment}
%
% \subsubsection{符号和缩略语说明}
% \label{sec:denotationfmt}
% \begin{environment}{denotation}
% 符号和缩略语说明。
%    \begin{macrocode}
\newenvironment{denotation}[1][2.5cm]{%
  \ifthu@degree@bachelor
    \ifthu@main@language@chinese
      \thu@chapter*[]{\thu@denotation@name}%
    \else
      \thu@chapter*{\thu@denotation@name}%
    \fi
  \else
    \ifthu@degree@graduate
      \thu@chapter*{\thu@denotation@name}%
    \else
      \thu@chapter*[]{\thu@denotation@name}%
    \fi
  \fi
  \begin{thu@denotation}[labelwidth=#1]%
}{%
  \end{thu@denotation}%
}
\newlist{thu@denotation}{description}{1}
\setlist[thu@denotation]{%
  nosep,
  font=\normalfont,
  align=left,
  leftmargin=!, % sum of the following 3 lengths
  labelindent=0pt,
  labelwidth=2.5cm,
  labelsep*=0.5cm,
  itemindent=0pt,
}
%    \end{macrocode}
% \end{environment}
%
%
% \subsubsection{致谢以及声明}
% \label{sec:ackanddeclare}
%
% \begin{environment}{acknowledgements}
% 定义致谢环境
%    \begin{macrocode}
\newcommand{\thu@statement@text@bachelor}{%
  本人郑重声明：所呈交的综合论文训练论文，是本人在导师指导下，独立进行研究工作所取得的成果。%
  尽我所知，除文中已经注明引用的内容外，本论文的研究成果不包含任何他人享有著作权的内容。%
  对本论文所涉及的研究工作做出贡献的其他个人和集体，均已在文中以明确方式标明。%
}
\newcommand{\thu@statement@text@graduate}{%
  本人郑重声明：所呈交的学位论文，是本人在导师指导下，独立进行研究工作所取得的成果%
  \ifx\thu@secret@level\@empty
    ，不包含涉及国家秘密的内容%
  \fi%
  。尽我所知，除文中已经注明引用的内容外，本学位论文的研究成果不包含任何他人享有著作权的内容。%
  对本论文所涉及的研究工作做出贡献的其他个人和集体，均已在文中以明确方式标明。%
}
\newcommand{\thu@signature}{签\hspace{1em}名：}
\newcommand{\thu@backdate}{日\hspace{1em}期：}
%    \end{macrocode}
%
% 定义致谢与声明环境。
%    \begin{macrocode}
\newenvironment{acknowledgements}{%
  \@mainmatterfalse
  \thu@end@appendix@ref@section
  \thu@chapter*{\thu@acknowledgements@name}%
}{%
}
%    \end{macrocode}
% \end{environment}
%
% \begin{macro}{statement}
% 声明页面样式和插入声明页（支持扫描文件替换）。
%    \begin{macrocode}
\thu@define@key{
  statement-page-style = {
    name = statement@page@style,
    choices = {
      auto,
      empty,
      plain,
    },
    default = auto,
  }
}
\newcommand\statement[1][]{%
  \@mainmatterfalse
  \thu@end@appendix@ref@section
  \let\thu@statement@file\@empty
  \kv@define@key{thu@statement}{page-style}{\thusetup{statement-page-style=##1}}%
  \kv@define@key{thu@statement}{file}{\let\thu@statement@file\kv@value}%
  \kv@set@family@handler{thu@statement}{%
    \ifx\kv@value\relax
      \let\thu@statement@file\kv@key
    \else
      \kv@handled@false
    \fi
  }%
  \kvsetkeys{thu@statement}{#1}%
  \ifthu@statement@page@style@auto
    \ifx\thu@statement@file\@empty
      \thusetup{statement-page-style = empty}%
    \else
      \thusetup{statement-page-style = plain}%
    \fi
  \fi
  \ifx\thu@statement@file\@empty
    \thusetup{language=chinese}%
    \begingroup
      \ifthu@degree@graduate
        \ifthu@main@language@english
          \ctexset{%
            chapter/format = \centering\sffamily\fontsize{16bp}{20bp}\selectfont,
          }%
        \fi
      \fi
      \thu@chapter*{\thu@statement@name}%
    \endgroup
    \thispagestyle{\thu@statement@page@style}%
    \ifthu@degree@graduate
      \vspace{12bp}%
      \fontsize{12bp}{21bp}\selectfont
      \thu@statement@text@graduate\par
      \vspace{79bp}%
      \begingroup
        \noindent\hspace{5.4cm}\fontsize{13bp}{18bp}\selectfont
        \thu@signature\thu@underline[2.8cm]{}\hspace{-6bp}%
        \thu@backdate\thu@underline[2.3cm]{}\par
      \endgroup
    \else
      \ifthu@degree@bachelor
        \begingroup
          \renewcommand\CJKglue{\hspace{.1bp}}%
          \thu@statement@text@bachelor\par
        \endgroup
        \vspace{40bp}%
        \hfill 签\hspace{.5em}名：\thu@underline[2.75cm]{}\hspace{.5em}%
        日\hspace{.5em}期：\thu@underline[2.75cm]{}\par
      \fi
    \fi
    \thu@reset@main@language
  \else
    \includepdf[pagecommand={%
      \markboth{\thu@statement@name}{}%
      \thu@phantomsection
      \addcontentsline{toc}{chapter}{\thu@statement@name}%
      \thispagestyle{\thu@statement@page@style}%
    }]{\thu@statement@file}%
  \fi
}
%    \end{macrocode}
% \end{macro}
%
% 兼容旧版本保留 \env{acknowledgement}。
%    \begin{macrocode}
\let\acknowledgement\acknowledgements
\let\endacknowledgement\endacknowledgements
%    \end{macrocode}
%
% \subsubsection{插图和附表清单}
% \label{sec:threelists}
% 定义图表以及公式目录样式。
%    \begin{macrocode}
\def\thu@listof#1{% #1: float type
  \setcounter{tocdepth}{2}%  restore tocdepth in case being modified
  \@ifstar{\thu@deprecate{starred form of \protect\listof... command}{}}{}%
  \ifthu@degree@bachelor
    \ifthu@main@language@chinese
      \thu@chapter*[]{\csname list#1name\endcsname}%
    \else
      \thu@chapter*{\csname list#1name\endcsname}%
    \fi
  \else
    \ifthu@degree@graduate
      \thu@chapter*{\csname list#1name\endcsname}%
    \else
      \thu@chapter*[]{\csname list#1name\endcsname}%
    \fi
  \fi
  \@starttoc{\csname ext@#1\endcsname}%
}
%    \end{macrocode}
%
% \begin{macro}{\listoffigures}
% 插图清单。
%    \begin{macrocode}
\renewcommand\listoffigures{%
  \thu@listof{figure}%
}
\titlecontents{figure}
  [\z@]{}
  {\contentspush{\figurename~\thecontentslabel\thu@contents@label@delimiter}}{}
  {\thu@leaders\thecontentspage}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\listoftables}
% 附表清单。
%    \begin{macrocode}
\renewcommand\listoftables{%
  \thu@listof{table}%
}
\titlecontents{table}
  [\z@]{}
  {\contentspush{\tablename~\thecontentslabel\thu@contents@label@delimiter}}{}
  {\thu@leaders\thecontentspage}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\listoffiguresandtables}
% 将插图和附表合在一起列出“插图和附表清单”。
%    \begin{macrocode}
\newcommand\listoffiguresandtables{%
  \ifthu@degree@bachelor
    \ifthu@main@language@chinese
      \thu@chapter*[]{\thu@list@figure@table@name}%
    \else
      \thu@chapter*{\thu@list@figure@table@name}%
    \fi
  \else
    \ifthu@degree@graduate
      \thu@chapter*{\thu@list@figure@table@name}%
    \else
      \thu@chapter*[]{\thu@list@figure@table@name}%
    \fi
  \fi
  \@starttoc{lof}%
  \par
  \null\par
  \@starttoc{lot}%
}
%    \end{macrocode}
% \end{macro}
%
% 公式的 caption 已过时。
%    \begin{macrocode}
\def\ext@equation{loe}
\def\equcaption#1{%
  \thu@deprecate{"\protect\equcaption" command}{}%
  \addcontentsline{\ext@equation}{equation}%
                  {\protect\numberline{#1}}}
%    \end{macrocode}
%
% 公式索引 \cs{listofequations} 已过时。
%    \begin{macrocode}
\newcommand\listofequations{%
  \thu@deprecate{"\protect\listofequations" command}{}%
  \thu@listof{equation}%
}
\titlecontents{equation}
  [0pt]{\addvspace{6bp}}
  {\contentspush{\thu@equation@name~\thecontentslabel\thu@contents@label@delimiter}}{}
  {\thu@leaders\thecontentspage}
\contentsuse{equation}{loe}
%    \end{macrocode}
%
%
% \subsection{参考文献}
% \label{sec:ref}
%
% 参考文献的格式根据用户选择的 \BibTeX{}/BibLaTeX 分别进行配置，
% 所以使用 \pkg{filehook} 的方式。
%
% 设置 \option{cite-style} 的接口，只对 \BibTeX{} 的编译方式有效。
%    \begin{macrocode}
\thu@define@key{
  cite-style = {
    name = cite@style,
    choices = {
      super,
      inline,
      author-year,
    }
  }
}
%    \end{macrocode}
%
% \subsubsection{BibTeX + \pkg{natbib} 宏包}
%
%    \begin{macrocode}
\def\bibliographystyle#1{%
  \gdef\bu@bibstyle{#1}%
  \ifx\@begindocumenthook\@undefined\else
    \expandafter\AtBeginDocument
  \fi
    {\if@filesw
       \immediate\write\@auxout{\string\bibstyle{#1}}%
       \immediate\write\@auxout{\string\gdef\string\bu@bibstyle{#1}}%
     \fi}%
}
\def\bibliography#1{%
  \if@filesw
    \immediate\write\@auxout{\string\bibdata{\zap@space#1 \@empty}}%
    \immediate\write\@auxout{\string\gdef\string\bu@bibdata{#1}}%
  \fi
  \gdef\bu@bibdata{#1}%
  \@input@{\jobname.bbl}}
%    \end{macrocode}
%
% \BibTeX{} 和 \pkg{natbib} 宏包的配置。
%    \begin{macrocode}
\PassOptionsToPackage{compress}{natbib}
\AtEndOfPackageFile*{natbib}{
%    \end{macrocode}
% \begin{macro}{\inlinecite}
% 依赖于 \pkg{natbib} 宏包，修改其中的命令。 旧命令 \cs{onlinecite} 依然可用。
%    \begin{macrocode}
  \DeclareRobustCommand\inlinecite{\@inlinecite}
  \def\@inlinecite#1{\begingroup\let\@cite\NAT@citenum\citep{#1}\endgroup}
  \let\onlinecite\inlinecite
%    \end{macrocode}
% \end{macro}
%
% 几种种引用样式，与 \file{bst} 文件名保持一致，
% 这样在使用 \cs{bibliographystyle} 选择参考文献表的样式时也会设置对应的引用样式。
%    \begin{macrocode}
  \newcommand\bibstyle@super{%
    \bibpunct{[}{]}{,}{s}{,}{\textsuperscript{,}}}
  \newcommand\bibstyle@inline{%
    \bibpunct{[}{]}{,}{n}{,}{,}}
  \@namedef{bibstyle@author-year}{%
    \bibpunct{(}{)}{;}{a}{,}{,}}
%    \end{macrocode}
%
%    \begin{macrocode}
  \thu@option@hook{cite-style}{\@nameuse{bibstyle@\thu@cite@style}}
%    \end{macrocode}
%
% 几种种引用样式，与 \file{bst} 文件名保持一致，
% 这样在使用 \cs{bibliographystyle} 选择参考文献表的样式时也会设置对应的引用样式。
%    \begin{macrocode}
  \@namedef{bibstyle@thuthesis-numeric}{\citestyle{super}}
  \@namedef{bibstyle@thuthesis-author-year}{\citestyle{author-year}}
  \@namedef{bibstyle@cell}{\citestyle{author-year}}
  \@namedef{bibstyle@thuthesis-bachelor}{\citestyle{super}}
%    \end{macrocode}
%
% 修改引用的样式。
% 这里在 filehook 中无法使用 \cs{patchcmd}，所以只能手动重定义。
%
% 将 \cs{citep} super 式引用的页码改为上标。
%    \begin{macrocode}
  \renewcommand\NAT@citesuper[3]{%
    \ifNAT@swa
      \if*#2*\else
        #2\NAT@spacechar
      \fi
      % \unskip\kern\p@\textsuperscript{\NAT@@open#1\NAT@@close}%
      %  \if*#3*\else\NAT@spacechar#3\fi\else #1\fi\endgroup}
      \unskip\kern\p@
      \textsuperscript{%
        \NAT@@open#1\NAT@@close
        \if*#3*\else#3\fi
      }%
      \kern\p@
    \else
      #1%
    \fi
    \endgroup
  }
%    \end{macrocode}
%
% 将 \cs{citep} numbers 式引用的页码改为上标并置于括号外。
%    \begin{macrocode}
  \renewcommand\NAT@citenum[3]{%
    \ifNAT@swa
      \NAT@@open
      \if*#2*\else
        #2\NAT@spacechar
      \fi
      % #1\if*#3*\else\NAT@cmt#3\fi\NAT@@close
      #1\NAT@@close
      \if*#3*\else
        \textsuperscript{#3}%
      \fi
    \else
      #1%
    \fi
    \endgroup
  }
%    \end{macrocode}
%
% 修改 \cs{citet} 引用的样式。
%    \begin{macrocode}
  \def\NAT@citexnum[#1][#2]#3{%
    \NAT@reset@parser
    \NAT@sort@cites{#3}%
    \NAT@reset@citea
    \@cite{\def\NAT@num{-1}\let\NAT@last@yr\relax\let\NAT@nm\@empty
      \@for\@citeb:=\NAT@cite@list\do
      {\@safe@activestrue
      \edef\@citeb{\expandafter\@firstofone\@citeb\@empty}%
      \@safe@activesfalse
      \@ifundefined{b@\@citeb\@extra@b@citeb}{%
        {\reset@font\bfseries?}
          \NAT@citeundefined\PackageWarning{natbib}%
        {Citation `\@citeb' on page \thepage \space undefined}}%
      {\let\NAT@last@num\NAT@num\let\NAT@last@nm\NAT@nm
        \NAT@parse{\@citeb}%
        \ifNAT@longnames\@ifundefined{bv@\@citeb\@extra@b@citeb}{%
          \let\NAT@name=\NAT@all@names
          \global\@namedef{bv@\@citeb\@extra@b@citeb}{}}{}%
        \fi
        \ifNAT@full\let\NAT@nm\NAT@all@names\else
          \let\NAT@nm\NAT@name\fi
        \ifNAT@swa
        \@ifnum{\NAT@ctype>\@ne}{%
          \@citea
          \NAT@hyper@{\@ifnum{\NAT@ctype=\tw@}{\NAT@test{\NAT@ctype}}{\NAT@alias}}%
        }{%
          \@ifnum{\NAT@cmprs>\z@}{%
          \NAT@ifcat@num\NAT@num
            {\let\NAT@nm=\NAT@num}%
            {\def\NAT@nm{-2}}%
          \NAT@ifcat@num\NAT@last@num
            {\@tempcnta=\NAT@last@num\relax}%
            {\@tempcnta\m@ne}%
          \@ifnum{\NAT@nm=\@tempcnta}{%
            \@ifnum{\NAT@merge>\@ne}{}{\NAT@last@yr@mbox}%
          }{%
            \advance\@tempcnta by\@ne
            \@ifnum{\NAT@nm=\@tempcnta}{%
%    \end{macrocode}
%
% 在顺序编码制下，\pkg{natbib} 只有在三个以上连续文献引用才会使用连接号，
% 这里修改为允许两个引用使用连接号。
%    \begin{macrocode}
              % \ifx\NAT@last@yr\relax
              %   \def@NAT@last@yr{\@citea}%
              % \else
              %   \def@NAT@last@yr{--\NAT@penalty}%
              % \fi
              \def@NAT@last@yr{-\NAT@penalty}%
            }{%
              \NAT@last@yr@mbox
            }%
          }%
          }{%
          \@tempswatrue
          \@ifnum{\NAT@merge>\@ne}{\@ifnum{\NAT@last@num=\NAT@num\relax}{\@tempswafalse}{}}{}%
          \if@tempswa\NAT@citea@mbox\fi
          }%
        }%
        \NAT@def@citea
        \else
          \ifcase\NAT@ctype
            \ifx\NAT@last@nm\NAT@nm \NAT@yrsep\NAT@penalty\NAT@space\else
              \@citea \NAT@test{\@ne}\NAT@spacechar\NAT@mbox{\NAT@super@kern\NAT@@open}%
            \fi
            \if*#1*\else#1\NAT@spacechar\fi
            \NAT@mbox{\NAT@hyper@{{\citenumfont{\NAT@num}}}}%
            \NAT@def@citea@box
          \or
            \NAT@hyper@citea@space{\NAT@test{\NAT@ctype}}%
          \or
            \NAT@hyper@citea@space{\NAT@test{\NAT@ctype}}%
          \or
            \NAT@hyper@citea@space\NAT@alias
          \fi
        \fi
      }%
      }%
        \@ifnum{\NAT@cmprs>\z@}{\NAT@last@yr}{}%
        \ifNAT@swa\else
%    \end{macrocode}
%
% 将页码放在括号外边，并且置于上标。
%    \begin{macrocode}
          % \@ifnum{\NAT@ctype=\z@}{%
          %   \if*#2*\else\NAT@cmt#2\fi
          % }{}%
          \NAT@mbox{\NAT@@close}%
          \@ifnum{\NAT@ctype=\z@}{%
            \if*#2*\else
              \textsuperscript{#2}%
            \fi
          }{}%
          \NAT@super@kern
        \fi
    }{#1}{#2}%
  }%
%    \end{macrocode}
%
% 修改 \cs{citep} author-year 式的页码：
%    \begin{macrocode}
  \renewcommand\NAT@cite%
      [3]{\ifNAT@swa\NAT@@open\if*#2*\else#2\NAT@spacechar\fi
          % #1\if*#3*\else\NAT@cmt#3\fi\NAT@@close\else#1\fi\endgroup}
          #1\NAT@@close\if*#3*\else\textsuperscript{#3}\fi\else#1\fi\endgroup}
%    \end{macrocode}
%
% 修改 \cs{citet} author-year 式的页码：
%    \begin{macrocode}
  \def\NAT@citex%
    [#1][#2]#3{%
    \NAT@reset@parser
    \NAT@sort@cites{#3}%
    \NAT@reset@citea
    \@cite{\let\NAT@nm\@empty\let\NAT@year\@empty
      \@for\@citeb:=\NAT@cite@list\do
      {\@safe@activestrue
      \edef\@citeb{\expandafter\@firstofone\@citeb\@empty}%
      \@safe@activesfalse
      \@ifundefined{b@\@citeb\@extra@b@citeb}{\@citea%
        {\reset@font\bfseries ?}\NAT@citeundefined
                  \PackageWarning{natbib}%
        {Citation `\@citeb' on page \thepage \space undefined}\def\NAT@date{}}%
      {\let\NAT@last@nm=\NAT@nm\let\NAT@last@yr=\NAT@year
        \NAT@parse{\@citeb}%
        \ifNAT@longnames\@ifundefined{bv@\@citeb\@extra@b@citeb}{%
          \let\NAT@name=\NAT@all@names
          \global\@namedef{bv@\@citeb\@extra@b@citeb}{}}{}%
        \fi
      \ifNAT@full\let\NAT@nm\NAT@all@names\else
        \let\NAT@nm\NAT@name\fi
      \ifNAT@swa\ifcase\NAT@ctype
        \if\relax\NAT@date\relax
          \@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}\NAT@date}%
        \else
          \ifx\NAT@last@nm\NAT@nm\NAT@yrsep
              \ifx\NAT@last@yr\NAT@year
                \def\NAT@temp{{?}}%
                \ifx\NAT@temp\NAT@exlab\PackageWarningNoLine{natbib}%
                {Multiple citation on page \thepage: same authors and
                year\MessageBreak without distinguishing extra
                letter,\MessageBreak appears as question mark}\fi
                \NAT@hyper@{\NAT@exlab}%
              \else\unskip\NAT@spacechar
                \NAT@hyper@{\NAT@date}%
              \fi
          \else
            \@citea\NAT@hyper@{%
              \NAT@nmfmt{\NAT@nm}%
              \hyper@natlinkbreak{%
                \NAT@aysep\NAT@spacechar}{\@citeb\@extra@b@citeb
              }%
              \NAT@date
            }%
          \fi
        \fi
      \or\@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}}%
      \or\@citea\NAT@hyper@{\NAT@date}%
      \or\@citea\NAT@hyper@{\NAT@alias}%
      \fi \NAT@def@citea
      \else
        \ifcase\NAT@ctype
          \if\relax\NAT@date\relax
            \@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}}%
          \else
          \ifx\NAT@last@nm\NAT@nm\NAT@yrsep
              \ifx\NAT@last@yr\NAT@year
                \def\NAT@temp{{?}}%
                \ifx\NAT@temp\NAT@exlab\PackageWarningNoLine{natbib}%
                {Multiple citation on page \thepage: same authors and
                year\MessageBreak without distinguishing extra
                letter,\MessageBreak appears as question mark}\fi
                \NAT@hyper@{\NAT@exlab}%
              \else
                \unskip\NAT@spacechar
                \NAT@hyper@{\NAT@date}%
              \fi
          \else
            \@citea\NAT@hyper@{%
              \NAT@nmfmt{\NAT@nm}%
              \hyper@natlinkbreak{\NAT@spacechar\NAT@@open\if*#1*\else#1\NAT@spacechar\fi}%
                {\@citeb\@extra@b@citeb}%
              \NAT@date
            }%
          \fi
          \fi
        \or\@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}}%
        \or\@citea\NAT@hyper@{\NAT@date}%
        \or\@citea\NAT@hyper@{\NAT@alias}%
        \fi
        \if\relax\NAT@date\relax
          \NAT@def@citea
        \else
          \NAT@def@citea@close
        \fi
      \fi
      }}\ifNAT@swa\else
%    \end{macrocode}
%
% 将页码放在括号外边，并且置于上标。
%    \begin{macrocode}
        % \if*#2*\else\NAT@cmt#2\fi
        \if\relax\NAT@date\relax\else\NAT@@close\fi
        \if*#2*\else\textsuperscript{#2}\fi
      \fi}{#1}{#2}}
%    \end{macrocode}
%
% 参考文献表的正文部分用五号字。
% 行距采用固定值 16 磅，段前空 3 磅，段后空 0 磅。
%
% 本科生要求用五号字，行距采用固定值 16 磅，段前空 3 磅，段后空 0 磅。
% 采用悬挂格式，悬挂缩进 2 个汉字符或 1 厘米。
%
% 英文专业要求字号 10.5pt，行距用固定值 17pt，段前后 3pt，悬挂缩进 0.5inch（1.27 厘米）。
%
% 复用 \pkg{natbib} 的 \texttt{thebibliography} 环境，调整距离。
%    \begin{macrocode}
  \renewcommand\bibsection{\thu@chapter*{\bibname}}
  \newcommand\thu@set@bibliography@format{%
    \renewcommand\bibfont{\fontsize{10.5bp}{16bp}\selectfont}%
    \setlength{\bibsep}{3bp \@plus 3bp \@minus 3bp}%
    \ifthu@degree@bachelor
      \ifthu@main@language@chinese
        \setlength{\bibhang}{1cm}%
      \else
        \renewcommand\bibfont{\fontsize{10.5bp}{17bp}\selectfont}%
        \setlength{\bibsep}{6bp \@plus 3bp \@minus 3bp}%
        \setlength{\bibhang}{0.5in}%
      \fi
    \else
      \setlength{\bibhang}{1cm}%
    \fi
  }
  \thu@set@bibliography@format
  \thu@option@hook{degree}{\thu@set@bibliography@format}
  \thu@option@hook{main-language}{\thu@set@bibliography@format}
%    \end{macrocode}
%
% 研究生要求每一条文献的内容要尽量写在同一页内。
% 遇有被迫分页的情况，可通过“留白”或微调本页行距的方式尽量将同一条文献内容放在一页。
% 所以上述 \cs{bibsep} 的设置允许 3pt 的伸缩，
% 同时增加同一条文献内分页的惩罚，
% 这里参考 \href{https://github.com/plk/biblatex/blob/e5d6e69e61613cc33ab1fcc2083a8277eb9cfce5/tex/latex/biblatex/biblatex.def}{BibLaTeX 的设置}。
%    \begin{macrocode}
  \patchcmd\thebibliography{%
    \clubpenalty4000%
  }{%
    \interlinepenalty=5000\relax
    \clubpenalty=10000\relax
  }{}{\thu@patch@error{\thebibliography}}
  \patchcmd\thebibliography{%
    \widowpenalty4000%
  }{%
    \widowpenalty=10000\relax
  }{}{\thu@patch@error{\thebibliography}}
%    \end{macrocode}
%
% 参考文献表的编号居左，宽度 1 cm。
%    \begin{macrocode}
  \def\@biblabel#1{[#1]\hfill}
  \renewcommand\NAT@bibsetnum[1]{%
    % \settowidth\labelwidth{\@biblabel{#1}}%
    % \setlength{\leftmargin}{\labelwidth}%
    % \addtolength{\leftmargin}{\labelsep}%
    \setlength{\leftmargin}{1cm}%
    \setlength{\itemindent}{\z@}%
    \setlength{\labelsep}{0.1cm}%
    \setlength{\labelwidth}{0.9cm}%
    \setlength{\itemsep}{\bibsep}
    \setlength{\parsep}{\z@}%
    \ifNAT@openbib
      \addtolength{\leftmargin}{\bibindent}%
      \setlength{\itemindent}{-\bibindent}%
      \setlength{\listparindent}{\itemindent}%
      \setlength{\parsep}{0pt}%
    \fi
  }
}
%    \end{macrocode}
%
% \subsubsection{\pkg{biblatex} 宏包}
%
%    \begin{macrocode}
\AtEndOfPackageFile*{biblatex}{
  \AtBeginDocument{
    \ifthenelse{\equal{\blx@bbxfile}{apa}}{\def\bibname{REFERENCES}}{}
    \ifthenelse{\equal{\blx@bbxfile}{apa6}}{\def\bibname{REFERENCES}}{}
    \ifthenelse{\equal{\blx@bbxfile}{mla}}{\def\bibname{WORKS CITED}}{}
    \ifthenelse{\equal{\blx@bbxfile}{mla-new}}{\def\bibname{WORKS CITED}}{}
  }
  \DeclareRobustCommand\inlinecite{\parencite}
  \defbibheading{bibliography}[\bibname]{\thu@chapter*{\bibname}}
  \newcommand\thu@set@bibliography@format{%
    \renewcommand\bibfont{\fontsize{10.5bp}{16bp}\selectfont}%
    \setlength{\bibitemsep}{3bp \@plus 3bp \@minus 3bp}%
    \ifthu@degree@bachelor
      \ifthu@main@language@chinese
        \setlength{\bibhang}{1cm}%
      \else
        \renewcommand\bibfont{\fontsize{10.5bp}{17bp}\selectfont}%
        \setlength{\bibitemsep}{6bp \@plus 3bp \@minus 3bp}%
        \setlength{\bibhang}{0.5in}%
      \fi
    \else
      \setlength{\biblabelsep}{0.1cm}%
      \setlength{\bibhang}{1cm}%
    \fi
  }
  \thu@set@bibliography@format
  \thu@option@hook{degree}{\thu@set@bibliography@format}
  \thu@option@hook{main-language}{\thu@set@bibliography@format}
}
%    \end{macrocode}
%
% \subsubsection{\pkg{apacite} 宏包}
%
% \pkg{apacite} 在 \cs{begindocument} 处载入的 \file{english.apc}
% 会覆盖掉 \cs{bibname} 的定义，所以需要重新 \cs{thu@set@chapter@names}。
%    \begin{macrocode}
\AtEndOfPackageFile*{apacite}{
  \AtBeginDocument{
    \thu@set@chapter@names
  }
  \renewcommand\bibliographytypesize{\fontsize{10.5bp}{16bp}\selectfont}
  \setlength{\bibitemsep}{3bp \@plus 3bp \@minus 3bp}%
  \ifthu@degree@bachelor
    \ifthu@main@language@english
      \renewcommand\bibliographytypesize{\fontsize{10.5bp}{17bp}\selectfont}%
      \setlength{\bibitemsep}{6bp \@plus 3bp \@minus 3bp}%
    \fi
  \fi
  \ifthu@main@language@chinese
    \setlength{\bibleftmargin}{1cm}
    \setlength{\bibindent}{-\bibleftmargin}
  \else
    \setlength{\bibleftmargin}{0.5in}
    \setlength{\bibindent}{-\bibleftmargin}
  \fi
  \def\st@rtbibchapter{%
    \if@numberedbib%
      \chapter{\bibname}%   e.g.,   6. References
    \else%
      \thu@chapter*{\bibname}%   e.g.,   References
    \fi%
  }%
}
%    \end{macrocode}
%
% \subsection{附录}
% \label{sec:appendix}
%
%    \begin{macrocode}
\g@addto@macro\appendix{%
  \@mainmattertrue
}
%    \end{macrocode}
%
% 研究生和本科生的写作指南均未规定附录的节标题是否加入目录，
% 但是从示例来看，目录中只出现附录的 chapter 标题，
% 不出现附录中的 section 及 subsection 的标题。
% 部分院系（例如自动化系）的格式审查的老师甚至一致口头如此要求。
% （\href{https://github.com/tuna/thuthesis/pull/425}{\#425}）
%    \begin{macrocode}
\thu@define@key{
  toc-depth = {
    name = toc@depth,
  },
}
%    \end{macrocode}
%
% 这里不要使用 \cs{addcontentsline}，
% 避免写入 \pkg{titletoc} 的 \file{.ptc} 文件中，
% 造成 \env{survey} 的子目录中 |tocdepth| 为 0。
%    \begin{macrocode}
\thu@option@hook{toc-depth}{%
  \ifx\@begindocumenthook\@undefined
    \protected@write\@auxout{}{%
      \string\ttl@writefile{toc}{%
        \protect\setcounter{tocdepth}{\thu@toc@depth}%
      }%
    }%
  \else
    \setcounter{tocdepth}{\thu@toc@depth}%
  \fi
}
\g@addto@macro\appendix{%
  \thusetup{
    toc-depth = 0,
  }%
}
%    \end{macrocode}
%
% 附录中的图、表不列入插图清单/附表清单。
%    \begin{macrocode}
\thu@define@key{
  appendix-figure-in-lof = {
    name = appendix@figure@in@lof,
    choices = {
      true,
      false,
    },
    default = false,
  },
}
\thu@option@hook{appendix-figure-in-lof}{%
  \ifthu@appendix@figure@in@lof@true
    \addtocontents{lof}{\string\let\string\contentsline\string\ttl@contentsline}%
    \addtocontents{lot}{\string\let\string\contentsline\string\ttl@contentsline}%
    \addtocontents{loe}{\string\let\string\contentsline\string\ttl@contentsline}%
  \else
    \addtocontents{lof}{\string\let\string\contentsline\string\ttl@gobblecontents}%
    \addtocontents{lot}{\string\let\string\contentsline\string\ttl@gobblecontents}%
    \addtocontents{loe}{\string\let\string\contentsline\string\ttl@gobblecontents}%
  \fi
}
\g@addto@macro\appendix{%
  \thusetup{
    appendix-figure-in-lof = false,
  }%
}
%    \end{macrocode}
%
% 附录中的参考文献等另行编序号。
%    \begin{macrocode}
\newcommand\thu@end@appendix@ref@section{}
%    \end{macrocode}
%
%    \begin{macrocode}
%    \end{macrocode}
%
% \pkg{bibunits} 在载入时会保存 \cs{bibliography} 和 \cs{bibliographystyle}，
% 所以在载入宏包前修改定义。
%    \begin{macrocode}
\AtBeginOfPackageFile*{bibunits}{
  \def\bibliography#1{%
    \if@filesw
      \immediate\write\@auxout{\string\bibdata{\zap@space#1 \@empty}}%
%    \end{macrocode}
%
% 正文的 \cs{bibliography} 同时设置附录参考文献的默认 \file{.bib} 数据库。
%    \begin{macrocode}
      \immediate\write\@auxout{\string\gdef\string\bu@bibdata{#1}}%
    \fi
    \@input@{\jobname.bbl}%
    \gdef\bu@bibdata{#1}%
  }
  \def\bibliographystyle#1{%
    \ifx\@begindocumenthook\@undefined\else
      \expandafter\AtBeginDocument
    \fi
      {\if@filesw
        \immediate\write\@auxout{\string\bibstyle{#1}}%
%    \end{macrocode}
%
% 正文的 \cs{bibliographystyle} 同时设置附录参考文献的默认 \file{.bst} 样式。
%    \begin{macrocode}
        \immediate\write\@auxout{\string\gdef\string\bu@bibstyle{#1}}%
      \fi}%
      \gdef\bu@bibstyle{#1}%
  }
}
\AtEndOfPackageFile*{bibunits}{
  \def\@startbibunit{%
    \global\let\@startbibunitorrelax\relax
    \global\let\@finishbibunit\@finishstartedbibunit
    \global\advance\@bibunitauxcnt 1
    \if@filesw
      {\endlinechar-1
%    \end{macrocode}
%
% 使附录 aux 文件的 \cs{gdef}\cs{@localbibstyle} 能够生效。
%    \begin{macrocode}
      \makeatletter
      \@input{\@bibunitname.aux}}%
      \immediate\openout\@bibunitaux\@bibunitname.aux
      \immediate\write\@bibunitaux{\string\bibstyle{\@localbibstyle}}%
    \fi
  }
  \def\bu@bibliography#1{%
    \putbib[#1]%
  }
  \def\bu@bibliographystyle#1{%
    \if@filesw
      \immediate\write\@bibunitaux{\string\gdef\string\@localbibstyle{#1}}%
    \fi
    \gdef\@localbibstyle{#1}%
  }
  \providecommand\printbibliography{\putbib\relax}%
  \g@addto@macro\appendix{%
    \renewcommand\@bibunitname{\jobname-appendix-\@alph\c@chapter}%
    \bibliographyunit[\chapter]%
    \renewcommand\bibsection{%
      \ctexset{section/numbering = false}%
      \section{\bibname}%
      \ctexset{section/numbering = true}%
    }%
%    \end{macrocode}
%
% 研究生附录的引用编号加前缀，如附录 A 的引用 [1] 为 [A.1]。
%    \begin{macrocode}
    \ifthu@degree@graduate
      \renewcommand\citenumfont{\@Alph\c@chapter.}%
      \renewcommand\@extra@binfo{@-\@alph\c@chapter}%
      \renewcommand\@extra@b@citeb{@-\@alph\c@chapter}%
      \renewcommand\bibnumfmt[1]{[\@Alph\c@chapter.#1]\hfill}%
    \fi
  }
  \renewcommand\thu@end@appendix@ref@section{%
    \bibliographyunit\relax
  }
  \AtEndDocument{\thu@end@appendix@ref@section}
  \renewcommand\thu@set@survey@bibheading{%
    \renewcommand\bibsection{%
      \begingroup
        \ctexset{
          section = {
            numbering = false,
            format = \centering\normalsize,
            beforeskip = 20pt,
            afterskip = 4pt,
          },
        }%
        \section{\bibname}%
      \endgroup
    }
  }%
%    \end{macrocode}
%
% 如果正文和附录引用了同一文献，\pkg{bibunits} 会给出无意义的警告，这里消除警告。
%    \begin{macrocode}
  % \let\@xtestdef\@gobbletwo  % This doesn't work
  \def\bibunits@rerun@warning{\relax}
}
\PassOptionsToPackage{defernumbers = true}{biblatex}
\AtEndOfPackageFile*{biblatex}{
  \DeclareRefcontext{appendix}{}
  \g@addto@macro\appendix{%
    \pretocmd\chapter{%
      \newrefsection
      \ifthu@degree@bachelor\else
        \@tempcnta=\c@chapter
        \advance\@tempcnta\@ne
        \newrefcontext[labelprefix = {\@Alph\@tempcnta.}]{appendix}%
      \fi
    }{}{\thu@patch@error{\chapter}}%
    \defbibheading{bibliography}[\bibname]{%
      \ctexset{section/numbering = false}%
      \section{#1}%
      \ctexset{section/numbering = true}%
    }%
  }
  \renewcommand\thu@set@survey@bibheading{%
    \defbibheading{bibliography}[\bibname]{%
      \vspace{20bp}%
      \begingroup
        \ctexset{
          section = {
            numbering = false,
            format = \centering\normalsize,
            beforeskip = 0pt,
            afterskip = 0pt,
          },
        }%
        \section{\bibname}%
      \endgroup
    }%
  }%
  \def\bibliographystyle#1{%
    \thu@warning{'bibliographystyle' invalid for 'biblatex'.}%
  }
}
%    \end{macrocode}
%
% 本科生《写作规范》有独特的要求：附录 A 为外文资料的调研阅读报告或书面翻译，
% 并且要分别附上独立的参考文献和外文资料的原文索引。
% 所以这里定义 \env{survey} 和 \env{translation} 专门处理这两种情况，
% 其中参考文献使用了 \pkg{bibunits} 宏包的功能。
%
% 注意 \pkg{titletoc} 在 2019/07/14 v2.11.1702 修改了 \cs{printcontents} 接口，
% 而且 \cs{@ifpackagelater} 只能用在导言区中，所以需要定义辅助宏。
%    \begin{macrocode}
\@ifpackagelater{titletoc}{2019/07/14}{
  \newcommand\thu@print@contents[5]{%
    \printcontents[#1]{#2}{#3}[#4]{}%
  }
}{
  \newcommand\thu@print@contents[5]{%
    \printcontents[#1]{#2}{#3}{\setcounter{tocdepth}{#4}#5}%
  }
}
%    \end{macrocode}
%
% \begin{environment}{survey}
% 外文资料的调研阅读报告。
%    \begin{macrocode}
\newenvironment{survey}{%
  \chapter{外文资料的调研阅读报告}%
  \thusetup{language = english}%
  \let\title\thu@appendix@title
  \let\maketitle\thu@appendix@maketitle
  \thu@set@partial@toc@format
  \renewcommand\tableofcontents{%
    \section*{Contents}%
    \thu@pdfbookmark{1}{Contents}%
    \thu@print@contents{survey}{l}{1}{2}{}%
    \vskip 20bp%
  }%
  \let\appendix\thu@appendix@appendix
  \thu@set@survey@bibheading
  \renewcommand\bibname{参考文献}%
  \startcontents[survey]%
}{%
  \stopcontents[survey]%
  \thu@reset@main@language % restore language
}
\newcommand\thu@set@appendix@bib@heading{}
%    \end{macrocode}
% \end{environment}
%
% \begin{environment}{translation}
% 外文资料的书面翻译。
%    \begin{macrocode}
\newenvironment{translation}{%
  \chapter{外文资料的书面翻译}%
  \thusetup{language = chinese}%
  \let\title\thu@appendix@title
  \let\maketitle\thu@appendix@maketitle
  \renewenvironment{abstract}{%
    \ctexset{
      section = {
        format    += \centering,
        numbering  = false,
      },
    }%
    \section{摘要}%
  }{%
    \par
    \ifx\thu@keywords\@empty\else
      \textbf{关键词：}\thu@clist@use{\thu@keywords}{；}\par
    \fi
  }%
  \thu@set@partial@toc@format
  \renewcommand\tableofcontents{%
    \section*{目录}%
    \thu@pdfbookmark{1}{目录}%
    \thu@print@contents{translation}{l}{1}{2}{}%
    \vskip 20bp%
  }%
  \let\appendix\thu@appendix@appendix
  \thu@set@survey@bibheading
  \startcontents[translation]%
}{%
  \stopcontents[translation]%
  \thu@reset@main@language % restore language
}
%    \end{macrocode}
% \end{environment}
%
% \begin{environment}{translation-index}
% 书面翻译对应的原文索引，区别于译文的参考文献。
%    \begin{macrocode}
\newenvironment{translation-index}{}{}
\AtEndOfPackageFile*{bibunits}{
  \newcounter{thu@translation@index}%
  \renewenvironment{translation-index}{%
    \global\advance\c@thu@translation@index\@ne
    \begin{bibunit}%
      \renewcommand\@bibunitname{\jobname-index-\arabic{thu@translation@index}}%
      \renewcommand\bibname{书面翻译对应的原文索引}%
      \thu@set@survey@bibheading
  }{%
    \end{bibunit}%
  }
}
\AtEndOfPackageFile*{biblatex}{
  \renewenvironment{translation-index}{%
    \endrefsection
    \begin{refsection}%
      \renewcommand\bibname{书面翻译对应的原文索引}%
      \thu@set@survey@bibheading
  }{%
    \end{refsection}%
  }
}
%    \end{macrocode}
% \end{environment}
%
% 调研阅读报告需要独立的标题，这里仿照了标准文档类的用法 \cs{title}, \cs{maketitle}。
%    \begin{macrocode}
\DeclareRobustCommand\thu@appendix@title[1]{\gdef\thu@appendix@@title{#1}}
\newcommand\thu@appendix@maketitle{%
  \par
  \begin{center}%
    \xiaosi[1.667]\thu@appendix@@title
  \end{center}%
  \par
}
%    \end{macrocode}
%
%    \begin{macrocode}
\newcommand\thu@set@partial@toc@format{%
  \titlecontents{section}
    [\z@]{}
    {\contentspush{\thecontentslabel\quad}}{}
    {\thu@leaders\thecontentspage}%
  \titlecontents{subsection}
    [1em]{}
    {\contentspush{\thecontentslabel\quad}}{}
    {\thu@leaders\thecontentspage}%
  \titlecontents{subsubsection}
    [2em]{}
    {\contentspush{\thecontentslabel\quad}}{}
    {\thu@leaders\thecontentspage}%
}
%    \end{macrocode}
%
% 书面翻译的附录。
%    \begin{macrocode}
\newcommand\thu@appendix@appendix{%
  \def\theHsection{\Hy@AlphNoErr{section}}%
  \setcounter{section}{0}%
  \setcounter{subsection}{0}%
  \renewcommand\thesection{\thechapter.\@Alph\c@section}%
}%
%    \end{macrocode}
%
% 调研阅读报告的参考文献（或书面翻译对应的外文资料的原文索引）标题用宋体小四号字，段前 20pt，段后 6pt，行距 20pt。
%    \begin{macrocode}
\newcommand\thu@set@survey@bibheading{}
%    \end{macrocode}
%
%
% \subsection{个人简历}
%
% \begin{environment}{resume}
% 个人简历发表文章等。
%    \begin{macrocode}
\newenvironment{resume}{%
  \@mainmatterfalse
  \thu@end@appendix@ref@section
  \thu@chapter*{\thu@resume@name}%
  \ctexset{section/numbering = false}%
  \ifthu@degree@bachelor
    \ctexset{section/aftertitle = {：\@@par}}%
  \else
    \ctexset{section/format += \centering}%
  \fi
  \ifthu@language@chinese
    \ctexset{
      subsection = {
        format     = \sffamily\fontsize{14bp}{20bp}\selectfont,
        numbering  = false,
        aftertitle = {：\@@par},
      },
    }%
    \setlist[achievements]{
      topsep     = 6bp,
      itemsep    = 6bp,
      leftmargin = 1cm,
      labelwidth = 1cm,
      labelsep   = 0pt,
      first      = {
        \ifthu@degree@graduate
          \fontsize{12bp}{16bp}\selectfont
        \fi
      },
      align      = left,
      label      = [\arabic*],
      resume     = achievements,
    }%
  \else
    \ctexset{
      subsection = {
        beforeskip = 0pt,
        afterskip  = 0pt,
        format     = \bfseries\normalsize,
        indent     = \parindent,
        numbering  = false,
      },
    }%
    \ifthu@degree@bachelor
      % 内容部分用Arial字体，字号15pt，行距采用固定值20pt， 段前后 0pt。
      \sffamily\fontsize{15bp}{20bp}\selectfont
    \fi
    \setlist[achievements]{
      topsep     = 0bp,
      itemsep    = 0bp,
      leftmargin = 1.75cm,
      labelsep   = 0.5cm,
      align      = right,
      label      = [\arabic*],
      resume     = achievements,
    }%
  \fi
}{}
%    \end{macrocode}
% \end{environment}
%
% 旧的 \cs{resumeitem} 和 \cs{researchitem} 已经过时。
%    \begin{macrocode}
\newcommand\resumeitem[1]{%
  \thu@error{The "\protect\resumeitem" is obsolete. Please update to the new format}%
}
\newcommand\researchitem[1]{%
  \thu@error{The "\protect\researchitem" is obsolete. Please update to the new format}%
}
%    \end{macrocode}
%
% \begin{environment}{achievements}
% 学术成果由 \env{achievements} 环境罗列。
%    \begin{macrocode}
\newlist{achievements}{enumerate}{1}
\setlist[achievements]{
  topsep     = 6bp,
  partopsep  = 0bp,
  itemsep    = 6bp,
  parsep     = 0bp,
  leftmargin = 10mm,
  itemindent = 0pt,
  align      = left,
  label      = [\arabic*],
  resume     = achievements,
}
%    \end{macrocode}
% \end{environment}
%
%    \begin{macrocode}
\newenvironment{publications}{%
  \thu@deprecate{"publications" environment}{"achievements"}%
  \begin{achievements}%
}{%
  \end{achievements}%
}
\newcommand\publicationskip{%
  \thu@error{The "\protect\publicationskip" is obsolete. Do not use it}%
}
%    \end{macrocode}
%
% \subsection{指导教师/小组学术评语}
% \begin{environment}{comments}
%    \begin{macrocode}
\NewEnviron{comments}[1][]{%
  \thu@end@appendix@ref@section
  \ifthu@degree@graduate
    \@mainmatterfalse
    \kv@define@key{thu@comments}{name}{\let\thu@comments@name\kv@value}%
    \kv@set@family@handler{thu@comments}{%
      \ifx\kv@value\relax
        \let\thu@comments@name\kv@key
      \else
        \kv@handled@false
      \fi
    }%
    \kvsetkeys{thu@comments}{#1}%
    \chapter{\thu@comments@name}%
    \BODY\clearpage
  \fi
}
%    \end{macrocode}
% \end{environment}
%
% \subsection{答辩委员会决议书}
% \begin{environment}{resolution}
%    \begin{macrocode}
\NewEnviron{resolution}{%
  \thu@end@appendix@ref@section
  \ifthu@degree@graduate
    \@mainmatterfalse
    \chapter{\thu@resolution@name}%
    \BODY\clearpage
  \fi
}
%    \end{macrocode}
% \end{environment}
%
% \subsection{综合论文训练记录表}
%
% \begin{macro}{\record}
% （本科生专用）插入综合论文训练记录表的 PDF 版本，并加入书签。
%
%    \begin{macrocode}
\newcommand{\record}[1]{%
  \ifthu@degree@bachelor
    \let\thu@record@file\@empty
    \kv@define@key{thu@record}{file}{\let\thu@record@file\kv@value}%
    \kv@set@family@handler{thu@record}{%
      \ifx\kv@value\relax
        \let\thu@record@file\kv@key
      \else
        \kv@handled@false
      \fi
    }%
    \kvsetkeys{thu@record}{#1}%
    \ifx\thu@record@file\@empty
      \thu@error{File path of \protect\record\space is required}
    \fi
    \clearpage
    \thu@pdfbookmark{0}{综合论文训练记录表}%
    \includepdf[pages=-]{\thu@record@file}%
  \fi
}
%    \end{macrocode}
%
% \end{macro}
%
% \subsection{其他宏包的设置}
%
% 这些宏包并非格式要求，但是为了方便同学们使用，在这里进行简单设置。
%
% \subsubsection{\pkg{hyperref} 宏包}
%
% 使用 \cs{PassOptionsToPackage} 的方式进行配置，允许用户在 \cs{usepackage}
% 覆盖配置（\href{https://github.com/tuna/thuthesis/issues/863}{tuna/thuthesis\#863}）。
%
%    \begin{macrocode}
\PassOptionsToPackage{
  linktoc            = all,
  bookmarksdepth     = 2,
  bookmarksnumbered  = true,
  bookmarksopen      = true,
  bookmarksopenlevel = 1,
  bookmarksdepth     = 3,
  unicode            = true,
  psdextra           = true,
  breaklinks         = true,
  plainpages         = false,
  pdfdisplaydoctitle = true,
  hidelinks,
}{hyperref}
\AtEndOfPackageFile*{hyperref}{
  \newcounter{thu@bookmark}
  \renewcommand\thu@pdfbookmark[2]{%
    \phantomsection
    \stepcounter{thu@bookmark}%
    \pdfbookmark[#1]{#2}{thuchapter.\thethu@bookmark}%
  }
  \renewcommand\thu@phantomsection{%
    \phantomsection
  }
  \pdfstringdefDisableCommands{%
    \let\\\relax
    \let\quad\relax
    \let\qquad\relax
    \let\hspace\@gobble
  }%
%    \end{macrocode}
%
% \pkg{hyperref} 与 \pkg{unicode-math} 存在一些兼容性问题，见
% \href{https://github.com/ustctug/ustcthesis/issues/223}{%
%   ustctug/ustcthesis\#223}，
% \href{https://github.com/ho-tex/hyperref/pull/90}{ho-tex/hyperref\#90} 和
% \href{https://github.com/ustctug/ustcthesis/issues/235}{%
%   ustctug/ustcthesis/\#235}。
%    \begin{macrocode}
  \@ifpackagelater{hyperref}{2019/04/27}{}{%
    \g@addto@macro\psdmapshortnames{\let\mu\textmu}
  }%
  \ifthu@main@language@chinese
    \hypersetup{
      pdflang = zh-CN,
    }%
  \else
    \hypersetup{
      pdflang = en-US,
    }%
  \fi
  \AtBeginDocument{%
    \ifthu@main@language@chinese
      \hypersetup{
        pdftitle    = \thu@title,
        pdfauthor   = \thu@author,
        pdfsubject  = \thu@degree@category,
        pdfkeywords = \thu@keywords,
      }%
    \else
      \hypersetup{
        pdftitle    = \thu@title@en,
        pdfauthor   = \thu@author@en,
        pdfsubject  = \thu@degree@category@en,
        pdfkeywords = \thu@keywords@en,
      }%
    \fi
    \hypersetup{
      pdfcreator={\thuthesis-v\version}}
  }%
}
%    \end{macrocode}
%
% \subsubsection{\pkg{mathtools} 宏包}
%
% \pkg{mathtools} 会修改 \pkg{unicode-math} 的 \cs{underbrace} 和 \cs{overbrace}，
% 需要还原为 \cs{LaTeXunderbrace} 和 \cs{LaTeXoverbrace}，
% 参考 \url{https://tex.stackexchange.com/q/521394/82731}。
%    \begin{macrocode}
\AtEndOfPackageFile*{mathtools}{
  \@ifpackageloaded{unicode-math}{
    \let\underbrace\LaTeXunderbrace
    \let\overbrace\LaTeXoverbrace
  }{}
}
%    \end{macrocode}
%
% \subsubsection{\pkg{nomencl} 宏包}
%
%    \begin{macrocode}
\AtEndOfPackageFile*{nomencl}{
  \let\nomname\thu@denotation@name
  \def\thenomenclature{\begin{denotation}[\nom@tempdim]}
  \def\endthenomenclature{\end{denotation}}
}
%    \end{macrocode}
%
% \subsubsection{\pkg{siunitx} 宏包}
%
%    \begin{macrocode}
\AtEndOfPackageFile*{siunitx}{%
  \newcommand\thu@set@siunitx@language{%
    \ifthu@language@chinese
      \sisetup{
        list-final-separator = {\TextOrMath{\space}{\ }\text{和}\TextOrMath{\space}{\ }},
        list-pair-separator  = {\TextOrMath{\space}{\ }\text{和}\TextOrMath{\space}{\ }},
        range-phrase         = {\text{～}},
      }%
    \else
      \ifthu@language@english
        \sisetup{
          list-final-separator = {\TextOrMath{\space}{\ }\text{and}\TextOrMath{\space}{\ }},
          list-pair-separator  = {\TextOrMath{\space}{\ }\text{and}\TextOrMath{\space}{\ }},
          range-phrase         = {\TextOrMath{\space}{\ }\text{to}\TextOrMath{\space}{\ }},
        }%
      \fi
    \fi
  }
  \thu@set@siunitx@language
  \thu@option@hook{language}{\thu@set@siunitx@language}
}
%    \end{macrocode}
%
% \subsubsection{\pkg{amsthm} 宏包}
%
% 定理标题使用黑体，正文使用宋体，冒号隔开。
%    \begin{macrocode}
\AtEndOfPackageFile*{amsthm}{%
  \newtheoremstyle{thu}
    {\z@}{\z@}
    {\normalfont}{\z@}
    {\normalfont\sffamily}{\thu@theorem@separator}
    {0.5em}{}
  \theoremstyle{thu}
  \newtheorem{assumption}{\thu@assumption@name}[chapter]%
  \newtheorem{definition}{\thu@definition@name}[chapter]%
  \newtheorem{proposition}{\thu@proposition@name}[chapter]%
  \newtheorem{lemma}{\thu@lemma@name}[chapter]%
  \newtheorem{theorem}{\thu@theorem@name}[chapter]%
  \newtheorem{axiom}{\thu@axiom@name}[chapter]%
  \newtheorem{corollary}{\thu@corollary@name}[chapter]%
  \newtheorem{exercise}{\thu@exercise@name}[chapter]%
  \newtheorem{example}{\thu@example@name}[chapter]%
  \newtheorem{remark}{\thu@remark@name}[chapter]%
  \newtheorem{problem}{\thu@problem@name}[chapter]%
  \newtheorem{conjecture}{\thu@conjecture@name}[chapter]%
  \renewenvironment{proof}[1][\thu@proof@name]{\par
    \pushQED{\qed}%
    % \normalfont \topsep6\p@\@plus6\p@\relax
    \normalfont \topsep\z@\relax
    \trivlist
    \item[\hskip\labelsep
      %     \itshape
      % #1\@addpunct{.}]\ignorespaces
      \sffamily
      #1]\ignorespaces
  }{%
    \popQED\endtrivlist\@endpefalse
  }
  \renewcommand\qedsymbol{\thu@qed}
}
%    \end{macrocode}
%
% \subsubsection{\pkg{ntheorem} 宏包}
%
% 定理标题使用黑体，正文使用宋体，冒号隔开。
%    \begin{macrocode}
\AtEndOfPackageFile*{ntheorem}{%
  \theorembodyfont{\normalfont}%
  \theoremheaderfont{\normalfont\sffamily}%
  \theoremsymbol{\thu@qed}%
  \newtheorem*{proof}{\thu@proof@name}%
  \theoremstyle{plain}%
  \theoremsymbol{}%
  \theoremseparator{\thu@theorem@separator}%
  \newtheorem{assumption}{\thu@assumption@name}[chapter]%
  \newtheorem{definition}{\thu@definition@name}[chapter]%
  \newtheorem{proposition}{\thu@proposition@name}[chapter]%
  \newtheorem{lemma}{\thu@lemma@name}[chapter]%
  \newtheorem{theorem}{\thu@theorem@name}[chapter]%
  \newtheorem{axiom}{\thu@axiom@name}[chapter]%
  \newtheorem{corollary}{\thu@corollary@name}[chapter]%
  \newtheorem{exercise}{\thu@exercise@name}[chapter]%
  \newtheorem{example}{\thu@example@name}[chapter]%
  \newtheorem{remark}{\thu@remark@name}[chapter]%
  \newtheorem{problem}{\thu@problem@name}[chapter]%
  \newtheorem{conjecture}{\thu@conjecture@name}[chapter]%
}
%    \end{macrocode}
%
% \subsubsection{\pkg{algorithm} 宏包}
%
% 使 \env{algorithm} 和 \env{listing} 环境的名称随语言设置而改变，
% 并使其在附录中的编号规则与图、表等一致。
%
% \begin{macro}{\listofalgorithm}
% \begin{macro}{\listofalgorithm*}
%    \begin{macrocode}
\PassOptionsToPackage{chapter}{algorithm}
\AtEndOfPackageFile*{algorithm}{
  \floatname{algorithm}{\thu@algorithm@name}
  \renewcommand\listofalgorithms{%
    \thu@listof{algorithm}%
  }
  \renewcommand\listalgorithmname{\thu@list@algorithm@name}
  \def\ext@algorithm{loa}
  \contentsuse{algorithm}{loa}
  \titlecontents{algorithm}
    [\z@]{}
    {\contentspush{\fname@algorithm~\thecontentslabel\thu@contents@label@delimiter}}{}
    {\thu@leaders\thecontentspage}
}
%    \end{macrocode}
% \end{macro}
% \end{macro}
%
% \subsubsection{\pkg{algorithm2e} 宏包}
%
%    \begin{macrocode}
\PassOptionsToPackage{algochapter}{algorithm2e}
\AtEndOfPackageFile*{algorithm2e}{
  \renewcommand\algorithmcfname{\thu@algorithm@name}
  \SetAlgoCaptionLayout{thu@caption@font}
  \SetAlCapSty{relax}
  \SetAlgoCaptionSeparator{\hspace*{1em}}
  \SetAlFnt{\fontsize{11bp}{14.3bp}\selectfont}
  \renewcommand\listofalgorithms{%
    \thu@listof{algorithmcf}%
  }
  \renewcommand\listalgorithmcfname{\thu@list@algorithm@name}
  \def\ext@algorithmcf{loa}
  \contentsuse{algocf}{loa}
  \titlecontents{algocf}
    [\z@]{}
    {\contentspush{\algorithmcfname~\thecontentslabel\thu@contents@label@delimiter}}{}
    {\thu@leaders\thecontentspage}
}
%    \end{macrocode}
%
% \subsubsection{\pkg{minted} 宏包}
%
%    \begin{macrocode}
\AtEndOfPackageFile*{minted}{
  \newcommand\thu@set@listing@language{%
    \ifthu@language@chinese
      \floatname{listing}{代码}%
    \else
      \floatname{listing}{Listing}%
    \fi
  }
  \thu@set@listing@language
  \thu@option@hook{language}{\thu@set@listing@language}
}
%    \end{macrocode}
%
% \subsection{书脊}
% \label{sec:spine}
% \begin{macro}{\spine}
% 单独使用书脊命令会在新的一页产生竖排书脊，
% 参考 \url{https://tex.stackexchange.com/a/38585}。
%
% 本科生：
%   书脊的书写要求：用仿宋\_GB2312 字书写，字体大小根据论文的薄厚而定。
%   书脊上方写论文题目，下方写本科生姓名，距上下页边均为 3cm。
%
% 研究生：
%   博士论文的书脊使用三号字，硕士的为小三号。
%   示例中上下页边距为 5.5 cm，左右边距为 1 cm。
%    \begin{macrocode}
\thu@define@key{
  spine-font = {
    name = spine@font,
  },
  spine-title = {
    name = spine@title,
  },
  spine-author = {
    name = spine@author,
  },
}
\renewcommand\thu@spine@font{%
  \ifthu@degree@doctor
    \fontsize{16bp}{20.8bp}\selectfont
  \else
    \fontsize{15bp}{19.5bp}\selectfont
  \fi
}
\newcommand*\CJKmovesymbol[1]{\raise.3em\hbox{#1}}
\newcommand*\CJKmove{%
  \punctstyle{plain}%
  \let\CJKsymbol\CJKmovesymbol
  \let\CJKpunctsymbol\CJKsymbol
}
\NewDocumentCommand{\spine}{
    O{
      \ifx\thu@spine@title\@empty
        \thu@title
      \else
        \thu@spine@title
      \fi
    }
    O{
      \ifx\thu@spine@author\@empty
        \thu<AUTHOR>
        \thu@spine<AUTHOR>
    }}{%
  \clearpage
  \ifthu@degree@bachelor
    \newgeometry{
      vmargin = 3cm,
      hmargin = 1cm,
    }%
  \else
    \newgeometry{
      vmargin = 5.5cm,
      hmargin = 1cm,
    }%
  \fi
  \thispagestyle{empty}%
  \ifthu@main@language@chinese
    \thu@pdfbookmark{0}{书脊}%
  \else
    \thu@pdfbookmark{0}{Spine}%
  \fi
  \begingroup
    \noindent\hfill
    \rotatebox[origin=lt]{-90}{%
      \makebox[\textheight]{%
        \fangsong
        \addCJKfontfeatures*{RawFeature={vertical}}%
        \thu@spine@font
        \CJKmove
        #1\hfill
        \thu@stretch{4.5em}{#2}%
      }%
    }%
  \endgroup
  \clearpage
  \restoregeometry
}
%    \end{macrocode}
% \end{macro}
%
%
% \subsection{其它}
% \label{sec:other}
%
% 借用 \cls{ltxdoc} 和 \cls{l3doc} 里面的几个命令方便写文档。
%    \begin{macrocode}
\DeclareRobustCommand\cs[1]{\texttt{\char`\\#1}}
\DeclareRobustCommand\file{\nolinkurl}
\DeclareRobustCommand\env{\textsf}
\DeclareRobustCommand\pkg{\textsf}
\DeclareRobustCommand\cls{\textsf}
%    \end{macrocode}
%
%    \begin{macrocode}
\sloppy
%</cls>
%    \end{macrocode}
%
%
% \iffalse
%    \begin{macrocode}
%<*dtx-style>
\ProvidesPackage{dtx-style}
\RequirePackage{hypdoc}
\RequirePackage{ifthen}
\RequirePackage{fontspec}[2017/01/20]
\RequirePackage{amsmath}
\RequirePackage{unicode-math}
\RequirePackage{siunitx}
\RequirePackage[UTF8,scheme=chinese]{ctex}
\RequirePackage[
  top=2.5cm, bottom=2.5cm,
  left=4cm, right=2cm,
  headsep=3mm]{geometry}
\RequirePackage{hologo}
\RequirePackage{array,longtable,booktabs}
\RequirePackage{listings}
\RequirePackage{fancyhdr}
\RequirePackage{xcolor}
\RequirePackage{enumitem}
\RequirePackage{etoolbox}
\RequirePackage{metalogo}
\RequirePackage[tightLists=false]{markdown}

\markdownSetup{
  renderers = {
    link = {\href{#2}{#1}},
  }
}

\hypersetup{
  pdflang     = zh-CN,
  pdftitle    = {ThuThesis：清华大学学位论文模板},
  pdfauthor   = {清华大学 TUNA 协会},
  pdfsubject  = {清华大学学位论文模板使用说明},
  pdfkeywords = {论文模板; 清华大学; 使用说明},
  pdfdisplaydoctitle = true
}%

\setmainfont[
  Extension      = .otf,
  UprightFont    = *-regular,
  BoldFont       = *-bold,
  ItalicFont     = *-italic,
  BoldItalicFont = *-bolditalic,
]{texgyrepagella}
\setsansfont[
  Extension      = .otf,
  UprightFont    = *-regular,
  BoldFont       = *-bold,
  ItalicFont     = *-italic,
  BoldItalicFont = *-bolditalic,
]{texgyreheros}
\setmonofont[
  Extension      = .otf,
  UprightFont    = *-regular,
  BoldFont       = *-bold,
  ItalicFont     = *-italic,
  BoldItalicFont = *-bolditalic,
  Scale          = MatchLowercase,
  Ligatures      = CommonOff,
]{texgyrecursor}

\unimathsetup{
  math-style=ISO,
  bold-style=ISO,
}
\DeclareRobustCommand\mathellipsis{\mathinner{\unicodecdots}}
\IfFontExistsTF{XITSMath-Regular.otf}{
  \setmathfont[
    Extension    = .otf,
    BoldFont     = XITSMath-Bold,
    StylisticSet = 8,
  ]{XITSMath-Regular}
  \setmathfont[range={cal,bfcal},StylisticSet=1]{XITSMath-Regular.otf}
}{
  \setmathfont[
    Extension    = .otf,
    BoldFont     = *bold,
    StylisticSet = 8,
  ]{xits-math}
  \setmathfont[range={cal,bfcal},StylisticSet=1]{xits-math.otf}
}

\colorlet{thu@macro}{blue!60!black}
\colorlet{thu@env}{blue!70!black}
\colorlet{thu@option}{purple}
\patchcmd{\PrintMacroName}{\MacroFont}{\MacroFont\bfseries\color{thu@macro}}{}{}
\patchcmd{\PrintDescribeMacro}{\MacroFont}{\MacroFont\bfseries\color{thu@macro}}{}{}
\patchcmd{\PrintDescribeEnv}{\MacroFont}{\MacroFont\bfseries\color{thu@env}}{}{}
\patchcmd{\PrintEnvName}{\MacroFont}{\MacroFont\bfseries\color{thu@env}}{}{}

\def\DescribeOption{%
  \leavevmode\@bsphack\begingroup\MakePrivateLetters%
  \Describe@Option}
\def\Describe@Option#1{\endgroup
  \marginpar{\raggedleft\PrintDescribeOption{#1}}%
  \thu@special@index{option}{#1}\@esphack\ignorespaces}
\def\PrintDescribeOption#1{\strut \MacroFont\bfseries\sffamily\color{thu@option} #1\ }
\def\thu@special@index#1#2{\@bsphack
  \begingroup
    \HD@target
    \let\HDorg@encapchar\encapchar
    \edef\encapchar usage{%
      \HDorg@encapchar hdclindex{\the\c@HD@hypercount}{usage}%
    }%
    \index{#2\actualchar{\string\ttfamily\space#2}
           (#1)\encapchar usage}%
    \index{#1:\levelchar#2\actualchar
           {\string\ttfamily\space#2}\encapchar usage}%
  \endgroup
  \@esphack}

\lstdefinestyle{lstStyleBase}{%
   basicstyle=\small\ttfamily,
   aboveskip=\medskipamount,
   belowskip=\medskipamount,
   lineskip=0pt,
   boxpos=c,
   showlines=false,
   extendedchars=true,
   upquote=true,
   tabsize=2,
   showtabs=false,
   showspaces=false,
   showstringspaces=false,
   numbers=none,
   linewidth=\linewidth,
   xleftmargin=4pt,
   xrightmargin=0pt,
   resetmargins=false,
   breaklines=true,
   breakatwhitespace=false,
   breakindent=0pt,
   breakautoindent=true,
   columns=flexible,
   keepspaces=true,
   gobble=4,
   framesep=3pt,
   rulesep=1pt,
   framerule=1pt,
   backgroundcolor=\color{gray!5},
   stringstyle=\color{green!40!black!100},
   keywordstyle=\bfseries\color{blue!50!black},
   commentstyle=\slshape\color{black!60}}

\lstdefinestyle{lstStyleShell}{%
   style=lstStyleBase,
   frame=l,
   rulecolor=\color{purple},
   language=bash}

\lstdefinestyle{lstStyleLaTeX}{%
   style=lstStyleBase,
   frame=l,
   rulecolor=\color{violet},
   language=[LaTeX]TeX}

\lstnewenvironment{latex}{\lstset{style=lstStyleLaTeX}}{}
\lstnewenvironment{shell}{\lstset{style=lstStyleShell}}{}

\setlist{nosep}

\DeclareDocumentCommand{\option}{m}{\textsf{#1}}
\DeclareDocumentCommand{\env}{m}{\texttt{#1}}
\DeclareDocumentCommand{\pkg}{s m}{%
  \textsf{#2}\IfBooleanF#1{\thu@special@index{package}{#2}}}
\DeclareDocumentCommand{\cls}{s m}{%
  \textsf{#2}\IfBooleanF#1{\thu@special@index{package}{#2}}}
\DeclareDocumentCommand{\file}{s m}{%
  \nolinkurl{#2}\IfBooleanF#1{\thu@special@index{file}{#2}}}
\newcommand{\myentry}[1]{%
  \marginpar{\raggedleft\color{purple}\bfseries\strut #1}}
\newcommand{\note}[2][Note]{{%
  \color{magenta}{\bfseries #1}\emph{#2}}}

\g@addto@macro\UrlBreaks{%
  \do0\do1\do2\do3\do4\do5\do6\do7\do8\do9%
  \do\A\do\B\do\C\do\D\do\E\do\F\do\G\do\H\do\I\do\J\do\K\do\L\do\M
  \do\N\do\O\do\P\do\Q\do\R\do\S\do\T\do\U\do\V\do\W\do\X\do\Y\do\Z
  \do\a\do\b\do\c\do\d\do\e\do\f\do\g\do\h\do\i\do\j\do\k\do\l\do\m
  \do\n\do\o\do\p\do\q\do\r\do\s\do\t\do\u\do\v\do\w\do\x\do\y\do\z
}
\Urlmuskip=0mu plus 0.1mu

\DeclareDocumentCommand{\githubuser}{m}{\href{https://github.com/#1}{@#1}}

\def\thuthesis{\textsc{Thu}\-\textsc{Thesis}}
%</dtx-style>
%    \end{macrocode}
% \fi
%
% \Finale
%
\endinput
% \iffalse
%  Local Variables:
%  mode: doctex
%  TeX-master: t
%  End:
% \fi
