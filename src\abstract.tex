% !TeX root = ../main.tex

% 中英文摘要和关键字

\begin{abstract}
  随着低空经济成为国家重点发展的新兴产业，无人机、电动垂直起降飞行器等低空飞行器在开放空域高密度运行是未来发展的必然趋势，而这对低空空域的有效管理提出了挑战。
  其中，低空飞行器监视系统的缺失是亟待解决的关键技术问题之一。
  现有的监视系统（通信协议）分别存在发射机容量低、传输距离小、带宽小的缺点，不能完全适应低空飞行器的特点和需要，也未得到广泛使用或确定为技术标准。
  本文针对这一技术瓶颈，基于ADS-B系统的设计理念，设计并实现了一种适用于低空飞行器的L波段广播式通信协议，具有发射机容量高、传输距离和带宽适中的特点。

  本文所描述的协议使用1438-1444MHz的L波段信道，采用16QAM调制方式，符号速率为1.5MHz。
  协议设计的先导码长度为29个符号，可以允许进行信号检测、载波同步、符号同步、自动增益控制和消息头检测等步骤，为正常接收正文符号做准备。
  正文部分采用ASTM F3411-22a标准定义的Open Drone ID作为报文内容，使用CCSDS (128,64) QC-LDPC码进行信道编码和软解码，具有较强的纠错能力。
  协议还设计了基于TDMA的自组织时隙多址接入机制，支持去中心化的网络接入。

  本文深入分析了低空经济发展背景下的空域管理需求和现有通信协议的局限性，在继承ADS-B系统自动性、相关性、广播性设计理念的基础上，确定了新协议的总体技术方案。选择1443MHz作为中心频率，带宽2MHz，采用16QAM调制方式，符号速率1.5Msps，理论发射机容量可达5000台，相比ADS-B系统提升6倍以上。协议设计采用分层架构，涵盖物理层、编码层和网络层三个层面。

  在物理层设计中，针对突发信号盲检测问题，使用了基于双滑动窗法的信号检测算法；设计了粗同步与精同步相结合的载波同步方案，可处理最大87.54kHz的频偏；采用前馈式定时误差估计算法实现符号同步，满足短包通信时延要求；使用7位Barker码实现消息头检测，确保帧同步可靠性。在编码层设计中，采用ASTM F3411-22a标准定义的Open Drone ID作为报文内容，涵盖基本ID、位置向量、运行描述、系统报文等5种核心报文类型；选用CCSDS (128,64) QC-LDPC码进行信道编码，在信噪比10dB时误码率小于$10^{-3}$，误帧率小于$10^{-2}$。在网络层设计中，利用GPS授时实现时钟同步，设计了基于自组织的时隙分配策略和碰撞检测机制，支持去中心化的网络接入。

  论文通过仿真全面验证了协议各层的性能指标。与ADS-B系统的对比分析表明，新协议在发射机容量、传输可靠性和实时性方面具有显著优势。最后，使用PlutoSDR软件无线电平台进行实机验证，成功实现了协议的完整收发流程，星座图清晰，接收消息无误码，证明了协议设计的工程可行性。

  本研究提出的L波段广播式通信协议为低空飞行器监视系统提供了一种高容量、高可靠性的技术方案，有效解决了现有协议容量不足的问题，对推动低空经济健康发展和保障低空空域安全具有重要的理论意义和实用价值。

  % 关键词用"英文逗号"分隔，输出时会自动处理为正确的分隔符
  \thusetup{
    keywords = {低空飞行器, 广播式运行识别, L波段, ADS-B, 软件无线电},
  }
\end{abstract}

\begin{abstract*}
  With the low-altitude economy emerging as a key national development priority, the explosive growth of low-altitude aircraft such as unmanned aerial vehicles (UAVs) and electric vertical takeoff and landing (eVTOL) aircraft has posed unprecedented challenges to airspace management. Although the existing civil aviation ADS-B system features advanced design concepts, its transmitter capacity of approximately 800 units cannot meet the surveillance requirements of high-density low-altitude aircraft operations. This paper addresses this technical bottleneck by designing and implementing an L-band broadcast communication protocol to provide a novel technical solution for low-altitude aircraft airspace management.

  This paper conducts an in-depth analysis of airspace management requirements under the low-altitude economic development context and the limitations of existing communication protocols. Building upon the design principles of automaticity, correlativity, and broadcast nature inherited from the ADS-B system, the overall technical scheme of the new protocol is established. The protocol adopts a center frequency of 1443MHz, bandwidth of 2MHz, 16QAM modulation, and symbol rate of 1.5Msps, achieving a theoretical transmitter capacity of 5000 units, representing more than a 6-fold improvement over the ADS-B system. The protocol design employs a layered architecture encompassing physical layer, coding layer, and network layer.

  In the physical layer design, a signal detection algorithm based on the dual sliding window method is used to address the blind detection problem of burst signals. A carrier synchronization scheme combining coarse and fine synchronization is designed to handle frequency offsets up to 87.54kHz. A feedforward timing error estimation algorithm is implemented for symbol synchronization to meet the latency requirements of short-packet communications. A 7-bit Barker code is employed for message header detection to ensure frame synchronization reliability. In the coding layer design, Open Drone ID defined by ASTM F3411-22a standard is adopted as message content, covering five core message types including Basic ID, Location/Vector, Operational Description, and System messages. CCSDS (128,64) QC-LDPC codes are selected for channel coding, achieving bit error rates below $10^{-3}$ and frame error rates below $10^{-2}$ at 10dB SNR. In the network layer design, GPS timing is utilized for clock synchronization, and a self-organizing time slot allocation strategy with collision detection mechanisms is designed to support decentralized network access.

  Comprehensive performance validation of each protocol layer is conducted through Python simulations. Comparative analysis with the ADS-B system demonstrates significant advantages of the new protocol in transmitter capacity, transmission reliability, and real-time performance. Finally, practical verification using the PlutoSDR software-defined radio platform successfully implements the complete protocol transceiver workflow, producing clear constellation diagrams and error-free message reception, proving the engineering feasibility of the protocol design.

  The L-band broadcast communication protocol proposed in this research provides a high-capacity, high-reliability technical solution for low-altitude aircraft surveillance systems, effectively addressing the capacity limitations of existing protocols. This work holds significant theoretical importance and practical value for promoting the healthy development of the low-altitude economy and ensuring low-altitude airspace safety.

  % Use comma as separator when inputting
  \thusetup{
    keywords* = {Low-altitude aircraft, Broadacst Remote identification, L-band, ADS-B, Software-defined radio},
  }
\end{abstract*}
