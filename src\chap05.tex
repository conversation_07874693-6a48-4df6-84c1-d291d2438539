% !TeX root = ../main.tex

\chapter{多址接入与发射机容量}

\section{多址接入}

在低空飞行器广播消息的应用场景中，大量发射机需要共享有限的信道资源，因此必须设计高效、可靠的多址接入机制。
考虑到远程识别信息对时延和公平性的要求，本协议采用时分多址（TDMA）技术。
该技术将时间划分为不重叠的时隙，很大程度上避免了传统随机接入方式（如CSMA/CA）在网络高负载下性能急剧下降的问题，保证了通信的确定性。

根据前两章前导码和编码方式的设计，现在每个报文传输所需的符号数为$29+98=127$个，即$127\times 667\text{ns} = 84.7\text{us}$。
考虑到保护时间需要至少10us（详见第2章），因此我们可以将单个时隙的长度确定为100us。

\subsection{利用GPS授时的时钟同步}

TDMA系统的有效运行依赖于网络内所有节点精确的时间同步。
若节点间存在显著的时钟偏差，将导致其发射的信号侵占相邻时隙，引发数据碰撞。
本方案利用全球定位系统（GPS）提供的授时服务来实现全网的时钟同步。

现代机载GPS接收机普遍具备高精度的授时能力，其输出的秒脉冲（1PPS）信号与协调世界时（UTC）的同步误差通常在100纳秒（ns）以内。
本协议设计的时隙宽度为100us，远大于GPS的授时误差。
这意味着，即便考虑到信号传播延迟等因素，所有飞行器仍能基于GPS时间将各自的时隙起点和终点对齐到极高的精度，确保了时隙的严格界分。
这种远超需求的精度裕量为TDMA系统的稳定运行提供了可靠保障，且无需设计额外的复杂同步协议，降低了系统复杂度和实现成本。

\subsection{消息广播频率与时隙划分}

根据《民用微轻小型无人驾驶航空器运行识别最低性能要求（试行）》的规定，对于广播式运行识别系统，动态报文（共一种，即位置向量报文）的更新速率至少为1秒1次；静态报文（共三种，包括基本ID报文、运行描述报文、系统报文）的更新速率则至少为3秒1次。

因此，我们将通信帧周期定为1秒，与监视系统常用的数据更新率保持一致。
同时，我们将通信帧均匀划分为两个区域，分别用于静态报文和动态报文的传输，如下图所示。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/timeslot.png}
  \caption{时隙划分示意图}
\end{figure}

每个区域内均有$0.5\text{s} \div 100\text{us} = 5000$个时隙，从0至5000编号。
每个发射机在通信帧中占用两个编号相同的时隙，分别广播位置向量报文和循环广播基本ID报文、运行描述报文、系统报文。
因此，我们设计的协议可承载发射机数量最大为5000台。

\subsection{碰撞检测机制与初始化}

本协议采用一种自组织的时隙分配策略，以实现网络的自主接入与冲突解决。
每个飞行器被分配的时隙在一次运行会话中是相对固定的，但在初始化或发生冲突时可以动态调整。

当飞行器首次开机或进入网络覆盖范围时，它首先会进入“监听模式”。
在此模式下，它将至少在一个完整的1秒帧周期内持续监听信道，解析接收到的所有报文，从而建立一张当前已被占用的时隙映射表（Slot Map）。
完成监听后，飞行器将从未占用时隙中，随机选择一个作为自己的发射时隙，并开始广播报文。

通过初始化时的监听，飞行器可以大概率避免选择已被占用的时隙，从而主动规避了接入冲突。
但是，当两个发射机同一秒进入网络覆盖范围、且恰好选择了同一时隙时，或者两个选择了同一个时隙的发射机相向运动，进入了各自的网络覆盖范围时，碰撞仍会发生。
对此，本方案依赖持续的信道监听来识别冲突。
如果一个飞行器在自己的发射时隙内，探测到非自身发出的、能量超过阈值的信号，即可判定发生碰撞。
一旦检测到碰撞，该飞行器将立即放弃当前时隙，并重复初始化的流程，重新监听并随机选择一个新的未占用时隙进行发射。

这种自组织的接入与冲突解决方法，无需中心化的地面站进行时隙分配，使网络具备了良好的可扩展性和鲁棒性。
它特别适合低空飞行器这种节点数量庞大、拓扑结构动态变化的分布式应用场景。

\section{与ADS-B系统的比较研究}

为了客观评估本设计协议在接收机容量方面的性能，本节以ADS-B系统作为比较对象，用仿真实验测试其接收机容量。

与本文提出的固定TDMA机制不同，ADS-B（1090ES）系统在信道接入方面更接近于一种随机接入机制。
飞机根据自身的飞行状态，按照规定的更新速率，间隔时间伪随机地向外广播其状态报文。
它没有统一的时隙结构，也未采用自组织时隙预留机制。这种设计简化了机载设备，但在高密度空域中也带来了固有的信道拥塞和消息碰撞风险。

\subsection{仿真设定}

为了测试得到ADS-B系统的发射机容量，我们仿真模拟了一个ADS-B共享信道。
我们先生成信道中所有发射机发射消息的开始时间，再检测信道中所有消息的发射时间是否重叠，来判断该消息发生碰撞或成功发送。
而通过观察不同数量的发射机下、信道里的消息丢失情况，我们可以对ADS-B系统正常运行时可承载的发射机数量做出评估。

以下是与仿真相关的ADS-B标准参数。

\begin{itemize}
  \item 消息长度：120us
  \item 位置消息更新间隔：0.4至0.6秒内均匀分布的随机变量
  \item 速度消息更新间隔：0.4至0.6秒内均匀分布的随机变量
  \item 身份消息更新间隔：4.8至5.2秒内均匀分布的随机变量
  \item 事件相关消息更新间隔：0.4至0.6秒内均匀分布的随机变量
\end{itemize}

以下是仿真器自身参数。

\begin{itemize}
  \item 仿真时间：60秒
  \item 预热时间：30秒（即在这段时间中不计算消息碰撞）
  \item 发射机初始化时间：0至5秒内均匀分布的随机变量
  \item 不设保护时间，不考虑信号传播延迟，单纯按消息长度是否有重叠来判断碰撞
\end{itemize}

\subsection{实验结果分析}

我们统计了30至90秒内信道内的所有消息，计算出消息因碰撞而丢失（重叠的两个消息都算作丢失）的概率如下图所示。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/failure_probability.png}
  \caption{ADS-B系统消息丢失概率与发射机数量的关系}
\end{figure}

可以看到消息丢失概率随着发射机数量的增加而迅速上升。
当信道中的发射机数量到达500左右时，消息丢失概率就已经超过了50\%，而当发射机数量为1000台时，消息丢失概率就已经接近80\%。

不过，我们无法难以根据消息丢失概率来人为地划定一个系统能正常工作的界限，来估算出最大发射机数量。
因此，我们对每个发射机计算了它前后两个成功发送的消息之间的时间间隔，并对其进行统计分析，如下图所示。
这个时间间隔是人们能够直接感知到性能差异的参数。
如果时间间隔过长，那么不仅是空中管制软件上数据更新的速率较慢，而且因为飞行器不能及时地将其位置、速度、甚至是紧急状态信息传递给管制员和其他飞行器，空域内的航路调整时间更长，更容易酿成悲剧。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/message_interval.png}
  \caption{ADS-B系统消息时间间隔与发射机数量的关系}
\end{figure}

从图中可以观察到，成功发送消息之间的时间间隔也随着发射机数量的增加而增加。
同时，我们在时间间隔的中位数曲线上可以看到阶梯形的趋势，这是因为ADS-B的最短消息间隔为0.4-0.6秒、且没有其他较短的消息间隔形式。
中位数曲线上一个台阶就意味着中位数对应的时间间隔增加了一个消息间隔的长度，或者说多跳过了一个消息。

当发射机数量为850台左右时，消息间隔的中位数上了一个台阶来到了1.5秒附近。
也就是说，此时大部分的发射机都需要间隔两条消息，才能成功发出一条消息。
同时，消息间隔的最大值也达到了20秒，超出了一般安全运行所规定的15秒，信道情况已经严重劣化。
因此，我们可以认为ADS-B系统可承载的最大发射机数量为850台左右。

这一估算也与实际情况相符。
一般ADS-B通信范围不超过300km，而以欧洲航班最繁忙地区的飞机密度为0.01架/平方海里计算，ADS-B覆盖范围的飞机数量最多为824架，仍在我们上面估算的发射机数量范围内。
因此，在没有跟高密度需求的前提下，ADS-B系统使用随机接入的方式也可以满足民航客机的监视需求。

\subsection{比较结论}

ADS-B系统可承载的最大发射机数量为850台左右，而本协议设计的发射机容量为5000台，是ADS-B的6倍左右。

同时，当ADS-B覆盖范围内有850台左右发射机时，成功发送的消息间隔中位数大于1.5秒，最大值在20秒左右，均超过《民用微轻小型无人驾驶航空器运行识别最低性能要求（试行）》中对动态报文规定的1秒1次的更新频率。
而本协议的设计可以保证动态报文的更新频率稳定为1秒1次（碰撞时可能丢失一次），较ADS-B系统更可靠。

因此，本协议较ADS-B系统在高密度低空飞行器的应用场景中更有优势。
