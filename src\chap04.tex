% !TeX root = ../main.tex

\chapter{报文内容与编码方案}

\section{报文内容和结构}

为了适应低空飞行器的飞行数据特征，我们采用ASTM F3411-22a标准中定义的Open Drone ID作为报文内容。\cite{ASTM2022}
Open Drone ID共规定了7种报文类型，每种报文（除消息包报文外）的长度为25字节。
其中第1个字节为报头，包含了该报文的类型与报文格式标准的版本号，后24字节则为不同报文类型的内容，如下所示。

\begin{table}[h]
\centering
\caption{报文头格式}
\begin{tabular}{|l|l|l|l|l|}
\hline
\textbf{起始字节} & \textbf{长度} & \textbf{字段名称} & \textbf{描述} & \textbf{备注} \\
\hline
0 & 0.5 & Message Type & 报文类型码 & 7-4位，见下 \\
\hline
0.5 & 0.5 & Protocol Version & 协议版本 & 3-0位，当前为0x2 \\
\hline
1 & 24 & Message & 报文内容 & \\
\hline
\end{tabular}
\label{tab:message_header}
\end{table}

在7种报文类型中，基本ID报文、位置向量报文、运行描述报文、系统报文，以及报文打包是承载低空飞行器运行系统数据的核心报文格式，同时也是《民用微轻小型无人驾驶航空器运行识别最低性能要求（试行）》中明文规定了的5种报文类型。
以下我们分别介绍这5种报文的主要内容、作用和数据结构。

\subsection{基本ID报文}

基本ID报文（Basic ID Message），类型码为0x0，属于周期性、强制静态报文\footnote{静态报文，即报文内容不随时间改变的报文}。
基本ID报文用于提供无人机的基本身份信息，它包含了无人机的唯一ID（UAS ID）、ID类型（例如，序列号或注册ID）以及无人机类型。

以下是基本ID报文的数据结构（按报文顺序从前至后）。

\begin{table}[h]
\centering
\caption{基本ID报文格式}
\begin{tabular}{|l|l|l|l|l|}
\hline
\textbf{起始字节} & \textbf{长度} & \textbf{字段名称} & \textbf{描述} & \textbf{备注} \\
\hline
1 & 0.5 & ID Type & 无人机ID的类型 & 7-4位，有枚举值 \\
\hline
1.5 & 0.5 & UA Type & 无人机类型 & 3-0位，有枚举值 \\
\hline
2 & 20 & UAS ID & 无人机的唯一标识符 & \\
\hline
22 & 3 & 保留 & 保留字段 & \\
\hline
\end{tabular}
\label{tab:basic_id_message}
\end{table}

\subsection{位置向量报文}

位置向量报文（Location/Vector Message），类型码为0x1，属于周期性、强制动态报文\footnote{动态报文，即报文内容随时间改变的报文}。
位置向量报文用于提供无人机的实时动态信息，包括当前的地理位置（经纬度）、高度、航向、地速、垂直速度等。它还包含了时间戳和位置精度信息。此报文对于跟踪无人机至关重要。

以下是位置向量报文的数据结构（按报文顺序从前至后）。

\begin{table}[h]
\centering
\caption{位置向量报文格式}
\begin{tabular}{|l|l|l|l|l|}
\hline
\makecell[l]{\textbf{起始}\\\textbf{字节}} & \textbf{长度} & \textbf{字段名称} & \textbf{描述} & \textbf{备注} \\
\hline
1 & 0.5 & Status & 运行状态 & 7-4位，有枚举值 \\
\hline
1.5 & 0.5 & Flags & 标志位 & \makecell[l]{3位：预留标志位，\\2位：高度类型位，\\1位：航迹角E/W方向标志，\\0位：速度乘数} \\
\hline
2 & 1 & Track direction & 航迹角 & 顺时针方向度数，0-179内整数 \\
\hline
3 & 1 & Speed & 地速 & 与速度乘数结合编码，最大254.25m/s \\
\hline
4 & 1 & Vertical Speed & 垂直速度 & 0.5m/s为单位的整数，范围\pm62m/s \\
\hline
5 & 4 & Latitude & 纬度 & 纬度$\times10^7$，有符号整数，使用小端序 \\
\hline
9 & 4 & Longitude & 经度 & 经度$\times10^7$，有符号整数，使用小端序 \\
\hline
13 & 2 & Pressure Altitude & 气压高度 & $(h+1000)/0.5$，无符号整数，使用小端序 \\
\hline
15 & 2 & Geodetic Altitude & 几何高度 & $(h+1000)/0.5$，无符号整数，使用小端序 \\
\hline
17 & 2 & Height & 距地高度 & $(h+1000)/0.5$，无符号整数，使用小端序 \\
\hline
19 & 0.5 & Horizontal Accuracy & 水平精度 & 7-4位，有枚举值 \\
\hline
19.5 & 0.5 & Vertical Accuracy & 垂直精度 & 3-0位，有枚举值 \\
\hline
20 & 0.5 & Baro Accuracy & \makecell[l]{气压高度\\精度} & 7-4位，有枚举值 \\
\hline
20.5 & 0.5 & Speed Accuracy & 速度精度 & 3-0位，有枚举值 \\
\hline
21 & 2 & Timestamp & 时间戳 & \makecell[l]{从当前小时开始的1/10秒数，\\无符号整数，使用小端序} \\
\hline
23 & 0.5 & 保留 & 保留字段 & 7-4位 \\
\hline
23.5 & 0.5 & Timestamp Accuracy & \makecell[l]{时间戳\\精度} & 3-0位，以1/10秒为单位的整数 \\
\hline
24 & 1 & 保留 & 保留字段 & \\
\hline
\end{tabular}
\label{tab:location_vector_message}
\end{table}

\subsection{运行描述报文}

运行描述报文（Self-ID Message），类型码为0x3，属于周期性、可选静态报文。
运行描述报文允许运行人填写文本来声明任务目的、或任何其他类型的补充信息。例如，“基础设施巡检”或“摄影测量”。这有助于公众了解无人机的活动性质。

以下是运行描述报文的数据结构（按报文顺序从前至后）。

\begin{table}[h]
\centering
\caption{运行描述报文格式}
\begin{tabular}{|l|l|l|l|l|}
\hline
\textbf{起始字节} & \textbf{长度} & \textbf{字段名称} & \textbf{描述} & \textbf{备注} \\
\hline
1 & 1 & Description Type & 描述的类型 & 默认为0，文字描述 \\
\hline
2 & 23 & Description & 描述文本 & ASCII编码，最多23个字符 \\
\hline
\end{tabular}
\label{tab:self_id_message}
\end{table}

\subsection{系统报文}

系统报文（System Message），类型码为0x4，属于周期性、可选静态报文。
系统报文提供有关无人机操作员/控制站的信息，例如控制站的位置（如果无人机是远程操控的）、航空器组群及额外的系统信息。

以下是系统报文的数据结构（按报文顺序从前至后）。

\begin{table}[h]
\centering
\caption{系统报文格式}
\begin{tabular}{|l|l|l|l|l|}
\hline
\makecell[l]{\textbf{起始}\\\textbf{字节}} & \textbf{长度} & \textbf{字段名称} & \textbf{描述} & \textbf{备注} \\
\hline
1 & 1 & Flags & 标志位 & \makecell[l]{7位：预留标志位，\\6-5位：坐标系类型，有枚举值,\\4-2位：等级分类归属区域，中国为0x2，\\1-0位：控制站位置类型，有枚举值} \\
\hline
2 & 4 & Op. Latitude & 控制站纬度 & 纬度$\times10^7$，有符号整数，使用小端序 \\
\hline
6 & 4 & Op. Longitude & 控制站经度 & 经度$\times10^7$，有符号整数，使用小端序 \\
\hline
10 & 2 & Area Count & 运行区域计数 & 运行区域内的航空器个数，使用小端序 \\
\hline
12 & 1 & Area Radius & 运行区域半径 & 以10m为单位的整数，使用小端序 \\
\hline
13 & 2 & Area Ceiling & 运行区域高度上限 & $(h+1000)/0.5$，无符号整数，使用小端序 \\
\hline
15 & 2 & Area Floor & 运行区域高度下限 & $(h+1000)/0.5$，无符号整数，使用小端序 \\
\hline
17 & 0.5 & UA Category & 无人机运行类别 & 7-4位，有枚举值 \\
\hline
17.5 & 0.5 & UA Class & 无人机等级 & 3-0位，有枚举值 \\
\hline
18 & 2 & Op. Altitude & 控制站高度 & $(h+1000)/0.5$，无符号整数，使用小端序 \\
\hline
20 & 4 & Timestamp & 时间戳 & \makecell[l]{从2019年1月1日开始的秒数，\\无符号整数，使用小端序} \\
\hline
24 & 1 & 保留 & 保留字段 & \\
\hline
\end{tabular}
\label{tab:system_message}
\end{table}

\subsection{报文打包}

报文打包（Message Pack），类型码为0xF。
报文打包允许将多个不同类型的报文打包成一条单一报文，从而提高发送效率。
报文打包的报文总长度为$25N+3$个字节，其中$N$为打包的报文数量，范围为2至10。

以下是系统报文的数据结构（按报文顺序从前至后）。

\begin{table}[h]
\centering
\caption{报文打包格式}
\begin{tabular}{|l|l|l|l|l|}
\hline
\textbf{起始字节} & \textbf{长度} & \textbf{字段名称} & \textbf{描述} & \textbf{备注} \\
\hline
1 & 1 & Message Size & 每个报文的长度 & 应为固定值0x19，即25字节 \\
\hline
2 & 1 & Message Count & 打包的报文数量 & \\
\hline
3 & 25N & Messages & 打包的报文 & 每25个字节为一个报文 \\
\hline
\end{tabular}
\label{tab:message_pack}
\end{table}


\section{信道编码}

在无线通信过程中，短时过大的噪声、干扰、以及多径衰落等不理想因素会使接收机在接收同步正常的情况下仍解码错误。
信道编码通过在传输时加入冗余信息，来提高接收端正确解码的概率，从而提高通信的可靠性。

当前主流的信道编码包括Turbo码、LDPC码、Polar码等。在本协议中，我们使用纠错性能强，编解码实现成本低的LDPC码作为信道编码方案。

\subsection{LDPC编码简介}

LDPC码是一类线性分组码，由Robert Gallager于1962年在其博士论文中首次提出。\cite{Gallager1962}
它的核心特点在于其拥有稀疏的校验矩阵 H，即矩阵中“1”的个数远少于“0”的个数。
尽管LDPC码提出的很早，但受限于当时的硬件计算能力，其优越性能在很长一段时间内未被充分发掘。
直到上世纪90年代，随着迭代译码算法（如置信传播算法）的发展和硬件技术的进步，LDPC码因其逼近香农限的优异性能而重新受到学术界和工业界的广泛关注。

LDPC码的具体编码形式并不固定。通过构造不同的校验矩阵H，我们可以灵活地设计出不同码率和码长的LDPC码，以适应不同的信道条件和业务需求。
一般我们记消息比特数量为M，校验比特（冗余比特）数量为K，则码长为$N=M+K$，码率为$R=\frac{M}{N}$，检验矩阵大小为$M\times N$。
我们可以将其简记为$(N, M)$LDPC码。

虽然在编码中长码长（$N>1000$）的消息时，LDPC码才能充分发挥其性能优势，
但是对于较短码长的消息（如本协议中的200比特长度），通过优化校验矩阵的结构，例如采用准循环LDPC码（QC-LDPC），仍然能够提供比传统简单分组码（如BCH码、RS码的短码版本）更好的性能。

准循环LDPC码指的是校验矩阵H通过一种结构化方式构造出来的LDPC码，其具有低差错平层（Error floor），可并行译码等优势，适合于低时延、短码的通信应用。
其校验矩阵H由一个基础矩阵P和一个扩展阵列定义，其中P为$p\times p$的方阵（p整除M和N），通常为单位循环移位矩阵，即
\begin{equation}
    P = \begin{bmatrix}
        0 & 1 & 0 & \cdots & 0 \\
        0 & 0 & 1 & \cdots & 0 \\
        \vdots & \vdots & \vdots & \ddots & \vdots \\
        0 & 0 & 0 & \cdots & 1 \\
        1 & 0 & 0 & \cdots & 0 \\
    \end{bmatrix}
\end{equation}
进一步地，我们可以定义$P^n$为将P向右循环移位n次得到的矩阵，而$P^0$表示不进行循环移位，即单位矩阵。

通过基础矩阵P，我们可以表示出校验矩阵H。它是一个分块矩阵，每个块矩阵都是P的多项式。
以一个$6\times9$的校验矩阵为例，其基础矩阵P为$3\times 3$的单位循环移位矩阵，则校验矩阵可以表示为
\begin{equation}
    H = \begin{bmatrix}
        P^0 & 0 & P^1 \\
        0 & P^0 & P^2 \\
    \end{bmatrix}
\end{equation}
这种表示方法即为其扩展阵列。

这意味着校验矩阵实际为
\begin{equation}
    H = \begin{bmatrix}
        1 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 0 \\
        0 & 1 & 0 & 0 & 0 & 0 & 0 & 0 & 1 \\
        0 & 0 & 1 & 0 & 0 & 0 & 1 & 0 & 0 \\
        0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 1 \\
        0 & 0 & 0 & 0 & 1 & 0 & 1 & 0 & 0 \\
        0 & 0 & 0 & 0 & 0 & 1 & 0 & 1 & 0 \\
    \end{bmatrix}
\end{equation}

\subsection{检验矩阵设计}

LDPC码的核心在于其校验矩阵H的构造，不同校验矩阵定义的LDPC码的性能可以有较大的差异。

由于构造和筛选性能优良的校验矩阵是一个需要灵感和大量计算的过程，因此我们并没有自己设计校验矩阵，而是直接使用了国际空间数据系统咨询委员会（CCSDS）为航天器控制指令通信设计的(128,64) QC-LDPC码。

该编码对应的校验矩阵的基础矩阵为$16\times 16$的单位循环移位矩阵，扩展阵列如下所示。\cite{CCSDS2021}
\begin{equation}
    H = \begin{bmatrix}
        P^0+P^7 & P^2 & P^{14} & P^6 & 0 & P^0 & P^{13} & P^0 \\
        P^6 & P^0+P^{15} & P^0 & P^1 & P^0 & 0 & P^0 & P^7 \\
        P^4 & P^1 & P^0+P^{15} & P^{14} & P^{11} & P^0 & 0 & P^3 \\
        P^0 & P^1 & P^9 & P^0+P^{13} & P^{14} & P^1 & P^0 & 0 \\
    \end{bmatrix}
\end{equation}

我们可以将展开后的校验矩阵矩阵通过二维图片的方式可视化，如下图所示。图中紫色代表零元，黄色代表一元。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/ldpc_H_matrix.png}
  \caption{LDPC校验矩阵可视化}
\end{figure}

经统计，校验矩阵的前64列列重（即非零元个数）为5，后64列列重为3，行重均为8。

\subsection{编码流程}

我们可以通过设计好的校验矩阵来计算出生成矩阵G，从而实现LDPC编码。
设校验矩阵H的前64列构成方阵A，后64列构成方阵B，即$H = [A | B]$。
则计算$W=B^{-1}A$，其中$B^{-1}$为B在GF(2)域下的逆矩阵\footnote{GF(2)域即二元域，加法和乘法运算对应异或和与运算。}，矩阵乘法也在GF(2)域下进行。
最后，生成矩阵G可以表示为$G = [I_{64} | W^T]$，其中$I_{64}$为$64\times 64$的单位矩阵。

同样地，我们可以通过二维图片的方式可视化计算出的生成矩阵，如下图所示。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/ldpc_G_matrix.png}
  \caption{LDPC生成矩阵可视化}
\end{figure}

当需要发送的消息比特序列为$\mathbf{m} = [m_0, m_1, \cdots, m_{63}]$时，我们可以通过与生成矩阵G进行矩阵乘法（在GF(2)域下进行）得到编码后的比特序列$\mathbf{c} = \mathbf{m}G$。
由于生成矩阵的前64列为单位矩阵，因此编码得到的前64位比特为原始消息，后64位则为冗余比特，即该(128,64) LDPC码为系统码。

\subsection{软解码算法}

LDPC码的优越性能很大程度上归功于其高效的迭代式软解码算法，这类算法通常属于消息传递算法的范畴。
其中最著名的是和积算法（SPA）或其对数域变种，如对数似然比置信传播（LLR-BP）算法。
这种解码算法利用了信道输出的软信息（即每个接收比特为0或1的概率或LLR值），而不是直接使用硬判决结果进行解码。

在这里我们使用对数似然比置信传播算法，并简要介绍其原理。\cite{Chen2005}

首先介绍Tanner图。Tanner图是一个二分图，包含两类节点：
\begin{itemize}
  \item 变量节点（Variable Node, VN），对应码字中的每一比特（即矩阵列，共N个）。
  \item 校验节点（Check Node, CN），对应校验矩阵H中的每一个校验方程（即矩阵行，共M个）。
\end{itemize}
如果校验矩阵H第i行第j列的元素非零（$H_{ij}=1$），则变量节点$v_j$和校验节点$c_i$之间有一条边相连。

算法解码过程基于Tanner图进行，变量节点和校验节点之间通过边传递“置信”或“消息”。

在迭代开始前，每个变量节点根据接收到的信道软信息（LLR值）初始化其置信值。
\begin{equation}
    L_V^{(0)}(v_j) = LLR(m_j) = \ln\frac{P(\hat{m_j}=1|m_j)}{P(\hat{m_j}=0|m_j)}
\end{equation}
其中$m_j$为接收的第j个比特信号，$P(\hat{m_j}=1|m_j)$和$P(\hat{m_j}=0|m_j)$分别为其为1和为0的后验概率。

除此以外的所有其他置信消息初始化值均为0。

然后开始迭代过程，迭代分两步。
在第一步中，每个变量节点$v_j$向其所有相连的校验节点$c_i$发送一个置信消息。
这个消息可以理解为当前变量节点（比特）的概率状况（更有可能是0还是1），其计算公式为
\begin{equation}
    L_{V \to C}^{(n+1)}(v_j, c_i) = L_V(v_j) + \sum_{k\in M(j), k\neq i} L_{C \to V}^{(n)}(c_k, v_j)
\end{equation}
其中$M(j)$为与变量节点$v_j$相连的所有校验节点的集合。

在第二步中， 每个校验节点$c_i$向其所有相连的变量节点$v_j$发送一个消息。
这个消息可以解释为“对于当前校验节点（校验方程），如果所有其他参与该方程的比特都取某个值时，比特$m_j$应该是什么”，其计算公式为
\begin{equation}
    L_{C \to V}^{(n+1)}(c_i, v_j) = 2\cdot \tanh^{-1}\left(\prod_{k\in N(i), k\neq j} \tanh\left(\frac{L_{V \to C}^{(n)}(v_k, c_i)}{2}\right)\right)
\end{equation}
其中$N(i)$为与校验节点$c_i$相连的所有变量节点的集合。

在一轮迭代结束后，我们计算每个变量节点的置信值并进行判决，公式为
\begin{equation}
    L_V^{(n)}(v_j) = L_V^{(0)}(v_j) + \sum_{k\in M(j)} L_{C \to V}^{(n)}(c_k, v_j)
\end{equation}
如果$L_V^{(n)}(v_j) \ge 0$，则判决为1，否则判决为0。

根据当前轮次判决出的码字，我们可以通过校验矩阵检验其是否为合法码字（$H\mathbf{c} = \mathbf{0}$）。
如果是合法码字，则认为解码成功，否则继续进行下一轮迭代，直至达到最大迭代次数。

\subsection{仿真结果}

我们使用随机BPSK比特序列和加性高斯白噪声信道（AWGN）来验证上述(128,64) LDPC码的性能。
下图给出了在不同比特信噪比条件下，解码得到的比特的误码率和误帧率（这里误帧率以解不出合法码字来计）

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/ldpc_error_rate.png}
  \caption{CCSDS (128,64) LDPC码的误码率和误帧率}
\end{figure}

可以看到，在$E_b/N_0$大于香农极限（1/2码率为0dB）后，误码率和误帧率迅速下降。
当$E_b/N_0$为4dB时（对应16QAM调制中信噪比为10dB），误码率已经小于$10^{-3}$，误帧率也小于$10^{-2}$，可以认为是可靠的。

同时，我们将上述LDPC编码方案应用于前面的报文上。
为了节约符号数，我们不对报文头的第一个字节进行信道编码，而是直接作为符号发送。
这是因为我们可以通过报文格式来推断报文类型，也可以通过时间划分来判断报文类型（详见第5章），即便这个字节接收出错，也不影响信息的传输。
而之后的24个字节则进行信道编码，每8个字节（64个比特）为一组进行编解码。
最后的消息正文长度为$8+128\times 3=392$个比特，符号数为$392\div4=98$个。

\section{星座映射}

如前所述，本协议在正文部分使用16QAM调制格式。星座图按照格雷码顺序排列，如下图所示。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.6\textwidth]{figures/constellation.png}
  \caption{16QAM符号星座图}
\end{figure}
