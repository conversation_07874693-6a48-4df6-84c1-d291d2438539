{"editor.detectIndentation": false, "editor.insertSpaces": true, "editor.tabSize": 2, "editor.indentSize": "tabSize", "files.autoSave": "onWindowChange", "files.autoSaveWhenNoErrors": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "latex-workshop.latex.recipes": [{"name": "latexmk (xelatex)", "tools": ["unpack-thuthesis", "xelatexmk"]}, {"name": "latexmk (lualatex)", "tools": ["unpack-thuthesis", "lualatexmk"]}, {"name": "make thesis", "tools": ["make-thesis"]}], "latex-workshop.latex.tools": [{"name": "unpack-thuthesis", "command": "xetex", "args": ["-file-line-error", "-halt-on-error", "-interaction=nonstopmode", "thuthesis.ins"], "env": {}}, {"name": "xelatexmk", "command": "latexmk", "args": ["-xelatex", "-file-line-error", "-halt-on-error", "-interaction=nonstopmode", "-synctex=1", "-outdir=%OUTDIR%", "%DOC%"], "env": {}}, {"name": "lualatexmk", "command": "latexmk", "args": ["-lualatex", "-file-line-error", "-halt-on-error", "-interaction=nonstopmode", "-synctex=1", "-outdir=%OUTDIR%", "%DOC%"], "env": {}}, {"name": "make-thesis", "command": "make", "args": [], "env": {}}], "latex-workshop.latex.watch.files.ignore": ["**/thuthesis.cls", "**/*.bbx", "**/*.bbl", "**/*.cbx", "**/*.cfg", "**/*.clo", "**/*.cnf", "**/*.def", "**/*.dfu", "**/*.enc", "**/*.fd", "**/*.fmt", "**/*.lbx", "**/*.map", "**/*.mkii", "**/*.pfb", "**/*.tfm", "**/*.vf", "**/*.code.tex", "**/*.sty", "**/texmf-{dist,var}/**", "**/Local/MiKTeX/**", "**/Local/Programs/MiKTeX/**", "**/Roaming/MiKTeX/**", "**/Program*/MiKTeX*/**", "**/.miktex/texmfs/**", "/var/cache/miktex-texmf/**", "/usr/local/share/miktex-texmf/**", "**/Library/Application Support/MiKTeX/texmfs/**", "/dev/null"]}